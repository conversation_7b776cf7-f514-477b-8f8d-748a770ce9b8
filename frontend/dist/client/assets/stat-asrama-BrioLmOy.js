import{c as h,a as s,t as d,m as f,v as w,s as y,F as p,p as g,o as u}from"./vendor-Xnl27S3x.js";import{_ as C}from"./index-CKhlfBVN.js";let c=null,o=null;const k=async()=>(c||await new Promise(t=>{const e=document.createElement("script");e.src="https://cdn.jsdelivr.net/npm/chart.js@3.8.0",e.onload=()=>{c=window.Chart,c.register(c.Title,c.<PERSON>ip,c.<PERSON>,c.ArcElement,c.CategoryScale,c.LinearScale),t()},document.head.appendChild(e)}),c),b={name:"StatAsrama",data(){return{selectedDate:"",selectedSesi:"",sesiOptions:[],referenceTime:"",statistics:[],chart:null,fetchedData:[],acara:"",isLoading:!1,sesiData:[]}},async created(){await this.fetchSesiData()},computed:{totalCount(){return this.statistics.reduce((t,e)=>t+Number(e.count),0)}},watch:{referenceTime:{handler(t){var e;(e=this.fetchedData)!=null&&e.length&&this.onReferenceTimeChange()},immediate:!1},acara:{handler(t){document.title=t?"Statistik Kehadiran Asrama - ".concat(t):"Statistik Kehadiran Asrama"},immediate:!0},selectedDate:{handler(t){t&&(console.log("Date changed to:",t),o&&(o.destroy(),o=null),this.fetchedData=[],this.statistics=[],this.fetchData())},immediate:!1},selectedSesi:{async handler(t){if(o&&(o.destroy(),o=null),t&&this.sesiData){const e=this.sesiData.find(i=>i.sesi===t);e!=null&&e.waktu&&(this.referenceTime=e.waktu)}this.fetchedData=[],this.statistics=[],this.fetchData()}}},methods:{async fetchSesiData(){try{const e=new URLSearchParams(window.location.search).get("sesi");if(!e){console.warn("Parameter sesi tidak ditemukan di URL");return}const i=encodeURIComponent(e),n=await fetch("/api/data/sesi/".concat(i));if(!n.ok)throw new Error("Network response was not ok");const r=await n.json();if(Array.isArray(r)){this.sesiData=r,this.sesiOptions=r.map(a=>a.sesi);const l=new URLSearchParams(window.location.search).get("time");if(l)this.referenceTime=l;else if(this.selectedSesi){const a=r.find(m=>m.sesi===this.selectedSesi);a!=null&&a.waktu&&(this.referenceTime=a.waktu)}this.referenceTime||(this.referenceTime="08:55")}}catch(t){console.error("Error fetching sesi data:",t),console.warn("Failed to fetch sesi data:",t.message),this.referenceTime="08:55"}},async fetchData(){if(this.isLoading){console.log("Request in progress, skipping new request");return}try{this.isLoading=!0,console.log("Fetching data for date:",this.selectedDate);const t=new URLSearchParams(window.location.search),e=t.get("key");this.acara=t.get("acara")||"";const i=t.get("lokasi")||"";let n="/api/absen-asramaan/?tanggal=".concat(this.selectedDate,"&acara=").concat(encodeURIComponent(this.acara),"&lokasi=").concat(encodeURIComponent(i));this.selectedSesi&&(n+="&sesi=".concat(encodeURIComponent(this.selectedSesi))),console.log("Requesting URL:",n);const r=await fetch(n,{headers:{Authorization:"ApiKey ".concat(e),Accept:"application/json"}});if(!r.ok)throw new Error("HTTP error! status: ".concat(r.status));const l=await r.json();if(!Array.isArray(l)){console.error("Invalid data format received:",l),this.handleNoData();return}this.fetchedData=l,this.processData(this.fetchedData)}catch(t){console.error("Error fetching data:",t),this.handleNoData(),console.warn("Failed to fetch attendance data: ".concat(t.message))}finally{this.isLoading=!1}},categorizeAttendance(t){if(!t)return"Unknown";const[e,i]=t.split(":").map(Number);if(Number.isNaN(e)||Number.isNaN(i))return"Unknown";const n=e*60+i,[r,l]=this.referenceTime.split(":").map(Number),a=r*60+l;return n<a?"In Time":n<=a+15?"On Time":"Terlambat"},initializeChart(t){var e;t.classList.add("chart-canvas"),(e=t.parentElement)==null||e.classList.add("chart-container")},async updateChart(t){try{const e=this.$refs.timePieChart;if(!e){console.error("Canvas element not found");return}e.classList.contains("chart-canvas")||this.initializeChart(e);const i=await k(),n=Object.keys(t),r=Object.values(t),l={"In Time":"#82EE84","On Time":"#36A2EB",Terlambat:"#FF2323",Unknown:"#CCCCCC"},a=n.map(D=>l[D]||"#CCCCCC");if(o){o.data.labels=n,o.data.datasets[0].data=r,o.update("none");return}const m={type:"pie",data:{labels:n,datasets:[{data:r,backgroundColor:a}]},options:{responsive:!0,maintainAspectRatio:!0,animation:{duration:300},plugins:{legend:{position:"bottom",labels:{boxWidth:20,padding:10}},title:{display:!0,text:"Distribusi Kehadiran Berdasarkan Waktu",padding:10}}}};o=new i(e,m)}catch(e){console.error("Error updating chart:",e),this.handleChartError(e)}},handleChartError(t){o&&(o.destroy(),o=null),console.error("Chart error:",t.message)},handleNoData(){o&&(o.destroy(),o=null),this.statistics=[],this.fetchedData=[],console.warn("No attendance data available for the selected date.")},processData(t){if(!Array.isArray(t)||!t.length){console.error("Invalid or empty data format:",t),this.handleNoData();return}try{const e=t.reduce((i,n)=>{if(!(n!=null&&n.jam_hadir))return i;const r=this.categorizeAttendance(n.jam_hadir);return r&&(i[r]=(i[r]||0)+1),i},{});if(Object.keys(e).length===0){this.handleNoData();return}requestAnimationFrame(()=>{this.updateChart(e),this.updateStatistics(e)})}catch(e){console.error("Error processing data:",e),this.handleNoData()}},updateStatistics(t){const e=Object.values(t).reduce((i,n)=>i+Number(n),0);this.statistics=Object.entries(t).map(([i,n])=>({category:i,count:n,percentage:(n/e*100).toFixed(1)}))},onReferenceTimeChange(){var t;console.log("Reference time changed to:",this.referenceTime),(t=this.fetchedData)!=null&&t.length&&(o&&(o.destroy(),o=null),this.processData(this.fetchedData))}},async mounted(){try{const t=new Date;this.selectedDate="".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")),await this.$nextTick();const e=this.$refs.dateInput;e&&e.addEventListener("change",i=>{this.selectedDate=i.target.value}),await this.fetchData()}catch(t){console.error("Error in mounted hook:",t),this.handleNoData()}}},S={id:"pieChartSection"},T={class:"mb-15"},v={class:"mb-10"},A={class:"mb-10"},N=["value"],E={ref:"timePieChart",width:"150",height:"150"},L={id:"statisticsTable"};function R(t,e,i,n,r,l){return u(),h("div",S,[e[10]||(e[10]=s("h2",null,"Statistik Kehadiran Asrama",-1)),s("div",T,d(r.acara||"ASRAMA"),1),s("div",v,[e[4]||(e[4]=s("label",{for:"tanggalFilter"},"Tanggal:",-1)),f(s("input",{type:"date",ref:"dateInput","onUpdate:modelValue":e[0]||(e[0]=a=>r.selectedDate=a),onChange:e[1]||(e[1]=(...a)=>l.fetchData&&l.fetchData(...a))},null,544),[[w,r.selectedDate]])]),s("div",A,[e[6]||(e[6]=s("label",{for:"sesiFilter"},"Sesi:",-1)),f(s("select",{"onUpdate:modelValue":e[2]||(e[2]=a=>r.selectedSesi=a),onChange:e[3]||(e[3]=(...a)=>l.fetchData&&l.fetchData(...a))},[e[5]||(e[5]=s("option",{value:""},"Semua Sesi",-1)),(u(!0),h(p,null,g(r.sesiOptions,a=>(u(),h("option",{key:a,value:a},d(a),9,N))),128))],544),[[y,r.selectedSesi]])]),s("canvas",E,null,512),s("table",L,[e[9]||(e[9]=s("thead",null,[s("tr",null,[s("th",null,"Kehadiran"),s("th",null,"Jumlah"),s("th",null,"Persentase")])],-1)),s("tbody",null,[(u(!0),h(p,null,g(r.statistics,a=>(u(),h("tr",{key:a.category},[s("td",null,d(a.category),1),s("td",null,d(a.count),1),s("td",null,d(a.percentage)+"%",1)]))),128)),s("tr",null,[e[7]||(e[7]=s("td",null,[s("strong",null,"Total")],-1)),s("td",null,[s("strong",null,d(l.totalCount),1)]),e[8]||(e[8]=s("td",null,[s("strong",null,"100%")],-1))])])])])}const F=C(b,[["render",R]]);export{F as default};
