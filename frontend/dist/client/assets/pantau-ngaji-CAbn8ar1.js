import{E as S}from"./jspdf.es.min-BEhHETZb.js";import"./jspdf.plugin.autotable-Bc4jgkkc.js";import{c as d,a as r,t as o,m as f,s as y,F as p,p as k,v as b,q as g,k as m,o as u}from"./vendor-Xnl27S3x.js";import{_ as c}from"./index-CKhlfBVN.js";const w={data(){return{attendanceData:[],filters:{ranah:"",kelompok:"",nama:"",tanggal:"",acara:"",lokasi:""},apiKey:"",sortKey:"",sortOrder:"asc"}},computed:{uniqueSesi(){return[...new Set(this.attendanceData.map(e=>e.sesi))].sort()},uniqueRanah(){return[...new Set(this.attendanceData.map(e=>e.ranah))].sort()},availableKelompok(){return this.filters.ranah?[...new Set(this.attendanceData.filter(e=>e.ranah===this.filters.ranah).map(e=>e.detail_ranah))].sort():[...new Set(this.attendanceData.map(e=>e.detail_ranah))].sort()},filteredData(){const e=this.attendanceData.filter(t=>{const i=!this.filters.sesi||t.sesi===this.filters.sesi,l=!this.filters.ranah||t.ranah===this.filters.ranah,s=!this.filters.kelompok||t.detail_ranah===this.filters.kelompok,n=!this.filters.nama||t.nama.toLowerCase().includes(this.filters.nama.toLowerCase());return i&&l&&s&&n});return this.sortKey&&e.sort((t,i)=>{let l=this.sortKey==="index"?1:t[this.sortKey],s=this.sortKey==="index"?1:i[this.sortKey];return typeof l=="string"&&(l=l.toLowerCase()),typeof s=="string"&&(s=s.toLowerCase()),l<s?this.sortOrder==="asc"?-1:1:l>s?this.sortOrder==="asc"?1:-1:0}),e}},methods:{filterTable(){},onDesaChange(){this.filters.kelompok="",this.filterTable()},formatDateIndonesian(e){const t=new Date(e),i=["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],l=["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"];return"".concat(i[t.getDay()],", ").concat(t.getDate()," ").concat(l[t.getMonth()]," ").concat(t.getFullYear())},async fetchDataForDate(){const e="/api/absen-pengajian/?tanggal=".concat(this.filters.tanggal,"&acara=").concat(this.filters.acara,"&lokasi=").concat(this.filters.lokasi||"");try{const t=await fetch(e,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));this.attendanceData=await t.json()}catch(t){console.error("Error fetching data:",t)}},downloadPDF(){const e=new S({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}});e.setFont("times","normal"),e.setFontSize(20),e.text("Laporan Kehadiran Acara",e.internal.pageSize.getWidth()/2,2,{align:"center"}),e.setFontSize(16),e.text("".concat(this.filters.acara," - ").concat(this.filters.ranah||"Semua"),e.internal.pageSize.getWidth()/2,2.8,{align:"center"}),e.text("".concat(this.formatDateIndonesian(this.filters.tanggal)),e.internal.pageSize.getWidth()/2,3.6,{align:"center"}),e.autoTable({head:[["No.","Nama","Kelompok","Jam Hadir"]],body:this.filteredData.map((i,l)=>[l+1,i.nama,i.detail_ranah,i.jam_hadir]),startY:4.5,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},columnStyles:{0:{cellWidth:2},3:{cellWidth:3}},didDrawPage:i=>{const l=i.pageNumber,s=e.internal.pageSize.height,n=e.internal.pageSize.width;e.setFontSize(10);const a=s-1.5;e.setDrawColor(200,200,200),e.setLineWidth(.02),e.line(1,a,n-1,a);const h=this.formatDateIndonesian(this.filters.tanggal),D="ABSENSI ".concat(this.filters.acara," - ").concat(this.filters.ranah," - ").concat(h," - Halaman ").concat(l);e.text(D,n-1,a+.5,{align:"right"})}});const t="Laporan-Absen-".concat(this.filters.ranah||"semua","-").concat(this.filters.acara||"UMUM","-").concat(this.filters.tanggal||"semua",".pdf");e.save(t)},sortTable(e){this.sortKey===e?this.sortOrder=this.sortOrder==="asc"?"desc":"asc":(this.sortKey=e,this.sortOrder="asc")},openStatistics(){const e="stat-ngaji.html?key=".concat(this.apiKey,"&acara=").concat(this.filters.acara,"&lokasi=").concat(this.filters.lokasi);window.open(e,"_blank")}},watch:{"filters.acara"(e){document.title="Pantauan Kehadiran Acara - ".concat(e||"UMUM")}},mounted(){const e=new URLSearchParams(window.location.search),t=new Date,i="".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"));this.apiKey=e.get("key"),this.filters.acara=e.get("acara")||"",this.filters.lokasi=e.get("lokasi")||"",this.filters.tanggal=e.get("tanggal")||i,this.fetchDataForDate(),document.title="Pantauan Kehadiran Acara - ".concat(this.filters.acara||"UMUM")}},F={id:"app"},v={style:{"margin-bottom":"15px","font-weight":"bold","text-align":"center"}},K={class:"filter-item"},T=["value"],C=["value"],M={class:"table-container"},x={id:"attendanceTable"},U={key:0},N={key:0},O={key:0},P={key:0},z={style:{display:"none"}},V={class:"button-container"};function A(e,t,i,l,s,n){return u(),d("div",F,[t[26]||(t[26]=r("h1",null,"Pantauan",-1)),r("div",v,o(s.filters.acara||"UMUM"),1),r("section",null,[r("div",K,[r("div",null,[t[15]||(t[15]=r("label",{for:"desaFilter"},"Filter Desa:",-1)),f(r("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>s.filters.ranah=a),onChange:t[1]||(t[1]=(...a)=>n.onDesaChange&&n.onDesaChange(...a)),id:"desaFilter"},[t[14]||(t[14]=r("option",{value:""},"Semua",-1)),(u(!0),d(p,null,k(n.uniqueRanah,a=>(u(),d("option",{key:a,value:a},o(a),9,T))),128))],544),[[y,s.filters.ranah]])]),r("div",null,[t[17]||(t[17]=r("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),f(r("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>s.filters.kelompok=a),onChange:t[3]||(t[3]=(...a)=>n.filterTable&&n.filterTable(...a)),id:"kelompokFilter"},[t[16]||(t[16]=r("option",{value:""},"Semua",-1)),(u(!0),d(p,null,k(n.availableKelompok,a=>(u(),d("option",{key:a,value:a},o(a),9,C))),128))],544),[[y,s.filters.kelompok]])]),r("div",null,[t[18]||(t[18]=r("label",{for:"tanggalFilter"},"Filter Tanggal:",-1)),f(r("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=a=>s.filters.tanggal=a),onInput:t[5]||(t[5]=(...a)=>n.fetchDataForDate&&n.fetchDataForDate(...a)),id:"tanggalFilter"},null,544),[[b,s.filters.tanggal]])]),r("div",null,[t[19]||(t[19]=r("label",{for:"namaFilter"},"Filter Nama:",-1)),f(r("input",{type:"text","onUpdate:modelValue":t[6]||(t[6]=a=>s.filters.nama=a),onInput:t[7]||(t[7]=(...a)=>n.filterTable&&n.filterTable(...a)),placeholder:"Cari nama...",id:"namaFilter"},null,544),[[b,s.filters.nama]])])])]),r("div",M,[r("table",x,[r("thead",null,[r("tr",null,[t[24]||(t[24]=r("th",null,"No.",-1)),r("th",{onClick:t[8]||(t[8]=a=>n.sortTable("nama"))},[t[20]||(t[20]=g(" Nama ",-1)),s.sortKey==="nama"?(u(),d("span",U,o(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[9]||(t[9]=a=>n.sortTable("ranah"))},[t[21]||(t[21]=g(" Desa ",-1)),s.sortKey==="ranah"?(u(),d("span",N,o(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[10]||(t[10]=a=>n.sortTable("detail_ranah"))},[t[22]||(t[22]=g(" Kelompok ",-1)),s.sortKey==="detail_ranah"?(u(),d("span",O,o(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[11]||(t[11]=a=>n.sortTable("jam_hadir"))},[t[23]||(t[23]=g(" Jam Hadir ",-1)),s.sortKey==="jam_hadir"?(u(),d("span",P,o(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),t[25]||(t[25]=r("th",{style:{display:"none"}},"Tanggal",-1))])]),r("tbody",null,[(u(!0),d(p,null,k(n.filteredData,(a,h)=>(u(),d("tr",{key:h},[r("td",null,o(h+1),1),r("td",null,o(a.nama),1),r("td",null,o(a.ranah),1),r("td",null,o(a.detail_ranah),1),r("td",null,o(a.jam_hadir),1),r("td",z,o(a.tanggal),1)]))),128))])])]),r("div",V,[r("button",{onClick:t[12]||(t[12]=(...a)=>n.downloadPDF&&n.downloadPDF(...a))},"Download as PDF"),r("button",{onClick:t[13]||(t[13]=(...a)=>n.openStatistics&&n.openStatistics(...a))},"View Statistics")])])}const I=c(w,[["render",A]]);export{I as default};
