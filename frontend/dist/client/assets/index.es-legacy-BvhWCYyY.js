System.register(["./jspdf.es.min-legacy-DQftmmnr.js","./index-legacy-D2-y4Zk0.js","./vendor-legacy-BmDVBcfe.js"],function(t,e){"use strict";var r;return{setters:[t=>{r=t._},null,null],execute:function(){t({CB1:Eu,CB2:Cu,CB3:Pu,CB4:Nu,QB1:Mu,QB2:Ru,QB3:_u,compressSpaces:au,getSelectorSpecificity:wu,normalizeAttributeName:cu,normalizeColor:fu,parseExternalUrl:lu,toNumbers:uu,trimLeft:su,trimRight:ou,vectorMagnitude:Tu,vectorsAngle:Au,vectorsRatio:Ou});var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var i,a,s={};function o(){if(a)return i;a=1;var t=function(t){return t&&t.Math===Math&&t};return i=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof e&&e)||t("object"==typeof i&&i)||function(){return this}()||Function("return this")()}var u,h,c,l,f,g,p,d,v={};function y(){return h?u:(h=1,u=function(t){try{return!!t()}catch(e){return!0}})}function m(){if(l)return c;l=1;var t=y();return c=!t(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})}function x(){if(g)return f;g=1;var t=y();return f=!t(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})}function b(){if(d)return p;d=1;var t=x(),e=Function.prototype.call;return p=t?e.bind(e):function(){return e.apply(e,arguments)},p}var w,S,T,O,A,E,C,P,N,M,R,_,V,I,k,L,D,j,B,F,z,U,H,X,Y,W,q,G,Q,$,Z,K,J,tt,et,rt,nt,it,at,st,ot,ut={};function ht(){return T?S:(T=1,S=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}})}function ct(){if(A)return O;A=1;var t=x(),e=Function.prototype,r=e.call,n=t&&e.bind.bind(r,r);return O=t?n:function(t){return function(){return r.apply(t,arguments)}},O}function lt(){if(C)return E;C=1;var t=ct(),e=t({}.toString),r=t("".slice);return E=function(t){return r(e(t),8,-1)}}function ft(){if(N)return P;N=1;var t=ct(),e=y(),r=lt(),n=Object,i=t("".split);return P=e(function(){return!n("z").propertyIsEnumerable(0)})?function(t){return"String"===r(t)?i(t,""):n(t)}:n}function gt(){return R?M:(R=1,M=function(t){return null==t})}function pt(){if(V)return _;V=1;var t=gt(),e=TypeError;return _=function(r){if(t(r))throw new e("Can't call method on "+r);return r}}function dt(){if(k)return I;k=1;var t=ft(),e=pt();return I=function(r){return t(e(r))}}function vt(){if(D)return L;D=1;var t="object"==typeof document&&document.all;return L=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(t){return"function"==typeof t}}function yt(){if(B)return j;B=1;var t=vt();return j=function(e){return"object"==typeof e?null!==e:t(e)}}function mt(){if(z)return F;z=1;var t=o(),e=vt();return F=function(r,n){return arguments.length<2?(i=t[r],e(i)?i:void 0):t[r]&&t[r][n];var i},F}function xt(){if(H)return U;H=1;var t=ct();return U=t({}.isPrototypeOf)}function bt(){if(Y)return X;Y=1;var t=o().navigator,e=t&&t.userAgent;return X=e?String(e):""}function wt(){if(q)return W;q=1;var t,e,r=o(),n=bt(),i=r.process,a=r.Deno,s=i&&i.versions||a&&a.version,u=s&&s.v8;return u&&(e=(t=u.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!e&&n&&(!(t=n.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=n.match(/Chrome\/(\d+)/))&&(e=+t[1]),W=e}function St(){if(Q)return G;Q=1;var t=wt(),e=y(),r=o().String;return G=!!Object.getOwnPropertySymbols&&!e(function(){var e=Symbol("symbol detection");return!r(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&t&&t<41})}function Tt(){if(Z)return $;Z=1;var t=St();return $=t&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}function Ot(){if(J)return K;J=1;var t=mt(),e=vt(),r=xt(),n=Tt(),i=Object;return K=n?function(t){return"symbol"==typeof t}:function(n){var a=t("Symbol");return e(a)&&r(a.prototype,i(n))}}function At(){if(et)return tt;et=1;var t=String;return tt=function(e){try{return t(e)}catch(r){return"Object"}}}function Et(){if(nt)return rt;nt=1;var t=vt(),e=At(),r=TypeError;return rt=function(n){if(t(n))return n;throw new r(e(n)+" is not a function")}}function Ct(){if(at)return it;at=1;var t=Et(),e=gt();return it=function(r,n){var i=r[n];return e(i)?void 0:t(i)}}function Pt(){if(ot)return st;ot=1;var t=b(),e=vt(),r=yt(),n=TypeError;return st=function(i,a){var s,o;if("string"===a&&e(s=i.toString)&&!r(o=t(s,i)))return o;if(e(s=i.valueOf)&&!r(o=t(s,i)))return o;if("string"!==a&&e(s=i.toString)&&!r(o=t(s,i)))return o;throw new n("Can't convert object to primitive value")}}var Nt,Mt,Rt,_t,Vt,It,kt,Lt,Dt,jt,Bt,Ft,zt,Ut,Ht,Xt,Yt,Wt,qt,Gt,Qt,$t,Zt,Kt,Jt={exports:{}};function te(){return Mt?Nt:(Mt=1,Nt=!1)}function ee(){if(_t)return Rt;_t=1;var t=o(),e=Object.defineProperty;return Rt=function(r,n){try{e(t,r,{value:n,configurable:!0,writable:!0})}catch(i){t[r]=n}return n}}function re(){if(Vt)return Jt.exports;Vt=1;var t=te(),e=o(),r=ee(),n="__core-js_shared__",i=Jt.exports=e[n]||r(n,{});return(i.versions||(i.versions=[])).push({version:"3.45.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.45.0/LICENSE",source:"https://github.com/zloirock/core-js"}),Jt.exports}function ne(){if(kt)return It;kt=1;var t=re();return It=function(e,r){return t[e]||(t[e]=r||{})}}function ie(){if(Dt)return Lt;Dt=1;var t=pt(),e=Object;return Lt=function(r){return e(t(r))}}function ae(){if(Bt)return jt;Bt=1;var t=ct(),e=ie(),r=t({}.hasOwnProperty);return jt=Object.hasOwn||function(t,n){return r(e(t),n)}}function se(){if(zt)return Ft;zt=1;var t=ct(),e=0,r=Math.random(),n=t(1.1.toString);return Ft=function(t){return"Symbol("+(void 0===t?"":t)+")_"+n(++e+r,36)}}function oe(){if(Ht)return Ut;Ht=1;var t=o(),e=ne(),r=ae(),n=se(),i=St(),a=Tt(),s=t.Symbol,u=e("wks"),h=a?s.for||s:s&&s.withoutSetter||n;return Ut=function(t){return r(u,t)||(u[t]=i&&r(s,t)?s[t]:h("Symbol."+t)),u[t]}}function ue(){if(qt)return Wt;qt=1;var t=function(){if(Yt)return Xt;Yt=1;var t=b(),e=yt(),r=Ot(),n=Ct(),i=Pt(),a=oe(),s=TypeError,o=a("toPrimitive");return Xt=function(a,u){if(!e(a)||r(a))return a;var h,c=n(a,o);if(c){if(void 0===u&&(u="default"),h=t(c,a,u),!e(h)||r(h))return h;throw new s("Can't convert object to primitive value")}return void 0===u&&(u="number"),i(a,u)}}(),e=Ot();return Wt=function(r){var n=t(r,"string");return e(n)?n:n+""}}function he(){if(Qt)return Gt;Qt=1;var t=o(),e=yt(),r=t.document,n=e(r)&&e(r.createElement);return Gt=function(t){return n?r.createElement(t):{}}}function ce(){if(Zt)return $t;Zt=1;var t=m(),e=y(),r=he();return $t=!t&&!e(function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a})}function le(){if(Kt)return v;Kt=1;var t=m(),e=b(),r=function(){if(w)return ut;w=1;var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,r=e&&!t.call({1:2},1);return ut.f=r?function(t){var r=e(this,t);return!!r&&r.enumerable}:t,ut}(),n=ht(),i=dt(),a=ue(),s=ae(),o=ce(),u=Object.getOwnPropertyDescriptor;return v.f=t?u:function(t,h){if(t=i(t),h=a(h),o)try{return u(t,h)}catch(c){}if(s(t,h))return n(!e(r.f,t,h),t[h])},v}var fe,ge,pe,de,ve,ye,me,xe={};function be(){if(ge)return fe;ge=1;var t=m(),e=y();return fe=t&&e(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})}function we(){if(de)return pe;de=1;var t=yt(),e=String,r=TypeError;return pe=function(n){if(t(n))return n;throw new r(e(n)+" is not an object")}}function Se(){if(ve)return xe;ve=1;var t=m(),e=ce(),r=be(),n=we(),i=ue(),a=TypeError,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,u="enumerable",h="configurable",c="writable";return xe.f=t?r?function(t,e,r){if(n(t),e=i(e),n(r),"function"==typeof t&&"prototype"===e&&"value"in r&&c in r&&!r[c]){var a=o(t,e);a&&a[c]&&(t[e]=r.value,r={configurable:h in r?r[h]:a[h],enumerable:u in r?r[u]:a[u],writable:!1})}return s(t,e,r)}:s:function(t,r,o){if(n(t),r=i(r),n(o),e)try{return s(t,r,o)}catch(u){}if("get"in o||"set"in o)throw new a("Accessors not supported");return"value"in o&&(t[r]=o.value),t},xe}function Te(){if(me)return ye;me=1;var t=m(),e=Se(),r=ht();return ye=t?function(t,n,i){return e.f(t,n,r(1,i))}:function(t,e,r){return t[e]=r,t}}var Oe,Ae,Ee,Ce,Pe,Ne,Me,Re,_e,Ve,Ie,ke,Le,De,je,Be={exports:{}};function Fe(){if(Ae)return Oe;Ae=1;var t=m(),e=ae(),r=Function.prototype,n=t&&Object.getOwnPropertyDescriptor,i=e(r,"name"),a=i&&"something"===function(){}.name,s=i&&(!t||t&&n(r,"name").configurable);return Oe={EXISTS:i,PROPER:a,CONFIGURABLE:s}}function ze(){if(Ce)return Ee;Ce=1;var t=ct(),e=vt(),r=re(),n=t(Function.toString);return e(r.inspectSource)||(r.inspectSource=function(t){return n(t)}),Ee=r.inspectSource}function Ue(){if(Re)return Me;Re=1;var t=ne(),e=se(),r=t("keys");return Me=function(t){return r[t]||(r[t]=e(t))}}function He(){return Ve?_e:(Ve=1,_e={})}function Xe(){if(ke)return Ie;ke=1;var t,e,r,n=function(){if(Ne)return Pe;Ne=1;var t=o(),e=vt(),r=t.WeakMap;return Pe=e(r)&&/native code/.test(String(r))}(),i=o(),a=yt(),s=Te(),u=ae(),h=re(),c=Ue(),l=He(),f="Object already initialized",g=i.TypeError,p=i.WeakMap;if(n||h.state){var d=h.state||(h.state=new p);d.get=d.get,d.has=d.has,d.set=d.set,t=function(t,e){if(d.has(t))throw new g(f);return e.facade=t,d.set(t,e),e},e=function(t){return d.get(t)||{}},r=function(t){return d.has(t)}}else{var v=c("state");l[v]=!0,t=function(t,e){if(u(t,v))throw new g(f);return e.facade=t,s(t,v,e),e},e=function(t){return u(t,v)?t[v]:{}},r=function(t){return u(t,v)}}return Ie={set:t,get:e,has:r,enforce:function(n){return r(n)?e(n):t(n,{})},getterFor:function(t){return function(r){var n;if(!a(r)||(n=e(r)).type!==t)throw new g("Incompatible receiver, "+t+" required");return n}}}}function Ye(){if(Le)return Be.exports;Le=1;var t=ct(),e=y(),r=vt(),n=ae(),i=m(),a=Fe().CONFIGURABLE,s=ze(),o=Xe(),u=o.enforce,h=o.get,c=String,l=Object.defineProperty,f=t("".slice),g=t("".replace),p=t([].join),d=i&&!e(function(){return 8!==l(function(){},"length",{value:8}).length}),v=String(String).split("String"),x=Be.exports=function(t,e,r){"Symbol("===f(c(e),0,7)&&(e="["+g(c(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!n(t,"name")||a&&t.name!==e)&&(i?l(t,"name",{value:e,configurable:!0}):t.name=e),d&&r&&n(r,"arity")&&t.length!==r.arity&&l(t,"length",{value:r.arity});try{r&&n(r,"constructor")&&r.constructor?i&&l(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(o){}var s=u(t);return n(s,"source")||(s.source=p(v,"string"==typeof e?e:"")),t};return Function.prototype.toString=x(function(){return r(this)&&h(this).source||s(this)},"toString"),Be.exports}function We(){if(je)return De;je=1;var t=vt(),e=Se(),r=Ye(),n=ee();return De=function(i,a,s,o){o||(o={});var u=o.enumerable,h=void 0!==o.name?o.name:a;if(t(s)&&r(s,h,o),o.global)u?i[a]=s:n(a,s);else{try{o.unsafe?i[a]&&(u=!0):delete i[a]}catch(c){}u?i[a]=s:e.f(i,a,{value:s,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return i},De}var qe,Ge,Qe,$e,Ze,Ke,Je,tr,er,rr,nr,ir,ar,sr,or,ur,hr,cr={};function lr(){if($e)return Qe;$e=1;var t=function(){if(Ge)return qe;Ge=1;var t=Math.ceil,e=Math.floor;return qe=Math.trunc||function(r){var n=+r;return(n>0?e:t)(n)},qe}();return Qe=function(e){var r=+e;return r!=r||0===r?0:t(r)}}function fr(){if(Ke)return Ze;Ke=1;var t=lr(),e=Math.max,r=Math.min;return Ze=function(n,i){var a=t(n);return a<0?e(a+i,0):r(a,i)},Ze}function gr(){if(tr)return Je;tr=1;var t=lr(),e=Math.min;return Je=function(r){var n=t(r);return n>0?e(n,9007199254740991):0}}function pr(){if(rr)return er;rr=1;var t=gr();return er=function(e){return t(e.length)}}function dr(){if(ir)return nr;ir=1;var t=dt(),e=fr(),r=pr(),n=function(n){return function(i,a,s){var o=t(i),u=r(o);if(0===u)return!n&&-1;var h,c=e(s,u);if(n&&a!=a){for(;u>c;)if((h=o[c++])!=h)return!0}else for(;u>c;c++)if((n||c in o)&&o[c]===a)return n||c||0;return!n&&-1}};return nr={includes:n(!0),indexOf:n(!1)}}function vr(){if(sr)return ar;sr=1;var t=ct(),e=ae(),r=dt(),n=dr().indexOf,i=He(),a=t([].push);return ar=function(t,s){var o,u=r(t),h=0,c=[];for(o in u)!e(i,o)&&e(u,o)&&a(c,o);for(;s.length>h;)e(u,o=s[h++])&&(~n(c,o)||a(c,o));return c},ar}function yr(){return ur?or:(ur=1,or=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}var mr,xr,br,wr,Sr,Tr,Or,Ar,Er,Cr,Pr,Nr,Mr,Rr,_r,Vr,Ir,kr,Lr,Dr,jr,Br,Fr,zr,Ur,Hr,Xr,Yr,Wr,qr,Gr,Qr,$r,Zr,Kr,Jr,tn,en,rn,nn,an,sn,on,un,hn,cn,ln,fn,gn,pn,dn,vn,yn,mn,xn,bn,wn,Sn,Tn,On,An,En,Cn,Pn,Nn,Mn,Rn,_n,Vn,In,kn,Ln,Dn,jn,Bn,Fn={};function zn(){if(br)return xr;br=1;var t=mt(),e=ct(),r=function(){if(hr)return cr;hr=1;var t=vr(),e=yr().concat("length","prototype");return cr.f=Object.getOwnPropertyNames||function(r){return t(r,e)},cr}(),n=(mr||(mr=1,Fn.f=Object.getOwnPropertySymbols),Fn),i=we(),a=e([].concat);return xr=t("Reflect","ownKeys")||function(t){var e=r.f(i(t)),s=n.f;return s?a(e,s(t)):e}}function Un(){if(Sr)return wr;Sr=1;var t=ae(),e=zn(),r=le(),n=Se();return wr=function(i,a,s){for(var o=e(a),u=n.f,h=r.f,c=0;c<o.length;c++){var l=o[c];t(i,l)||s&&t(s,l)||u(i,l,h(a,l))}},wr}function Hn(){if(Or)return Tr;Or=1;var t=y(),e=vt(),r=/#|\.prototype\./,n=function(r,n){var u=a[i(r)];return u===o||u!==s&&(e(n)?t(n):!!n)},i=n.normalize=function(t){return String(t).replace(r,".").toLowerCase()},a=n.data={},s=n.NATIVE="N",o=n.POLYFILL="P";return Tr=n}function Xn(){if(Er)return Ar;Er=1;var t=o(),e=le().f,r=Te(),n=We(),i=ee(),a=Un(),s=Hn();return Ar=function(o,u){var h,c,l,f,g,p=o.target,d=o.global,v=o.stat;if(h=d?t:v?t[p]||i(p,{}):t[p]&&t[p].prototype)for(c in u){if(f=u[c],l=o.dontCallGetSet?(g=e(h,c))&&g.value:h[c],!s(d?c:p+(v?".":"#")+c,o.forced)&&void 0!==l){if(typeof f==typeof l)continue;a(f,l)}(o.sham||l&&l.sham)&&r(f,"sham",!0),n(h,c,f,o)}}}function Yn(){if(Pr)return Cr;Pr=1;var t=o(),e=bt(),r=lt(),n=function(t){return e.slice(0,t.length)===t};return Cr=n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function Wn(){if(Mr)return Nr;Mr=1;var t=Yn();return Nr="NODE"===t}function qn(){if(_r)return Rr;_r=1;var t=o();return Rr=t}function Gn(){if(Lr)return kr;Lr=1;var t=yt();return kr=function(e){return t(e)||null===e}}function Qn(){if(jr)return Dr;jr=1;var t=Gn(),e=String,r=TypeError;return Dr=function(n){if(t(n))return n;throw new r("Can't set "+e(n)+" as a prototype")}}function $n(){if(Fr)return Br;Fr=1;var t=function(){if(Ir)return Vr;Ir=1;var t=ct(),e=Et();return Vr=function(r,n,i){try{return t(e(Object.getOwnPropertyDescriptor(r,n)[i]))}catch(a){}}}(),e=yt(),r=pt(),n=Qn();return Br=Object.setPrototypeOf||("__proto__"in{}?function(){var i,a=!1,s={};try{(i=t(Object.prototype,"__proto__","set"))(s,[]),a=s instanceof Array}catch(o){}return function(t,s){return r(t),n(s),e(t)?(a?i(t,s):t.__proto__=s,t):t}}():void 0),Br}function Zn(){if(Ur)return zr;Ur=1;var t=Se().f,e=ae(),r=oe()("toStringTag");return zr=function(n,i,a){n&&!a&&(n=n.prototype),n&&!e(n,r)&&t(n,r,{configurable:!0,value:i})}}function Kn(){if(Xr)return Hr;Xr=1;var t=Ye(),e=Se();return Hr=function(r,n,i){return i.get&&t(i.get,n,{getter:!0}),i.set&&t(i.set,n,{setter:!0}),e.f(r,n,i)}}function Jn(){if(Wr)return Yr;Wr=1;var t=mt(),e=Kn(),r=oe(),n=m(),i=r("species");return Yr=function(r){var a=t(r);n&&a&&!a[i]&&e(a,i,{configurable:!0,get:function(){return this}})}}function ti(){if(Gr)return qr;Gr=1;var t=xt(),e=TypeError;return qr=function(r,n){if(t(n,r))return r;throw new e("Incorrect invocation")}}function ei(){if(Kr)return Zr;Kr=1;var t=function(){if($r)return Qr;$r=1;var t={};return t[oe()("toStringTag")]="z",Qr="[object z]"===String(t)}(),e=vt(),r=lt(),n=oe()("toStringTag"),i=Object,a="Arguments"===r(function(){return arguments}());return Zr=t?r:function(t){var s,o,u;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(o=function(t,e){try{return t[e]}catch(r){}}(s=i(t),n))?o:a?r(s):"Object"===(u=r(s))&&e(s.callee)?"Arguments":u},Zr}function ri(){if(tn)return Jr;tn=1;var t=ct(),e=y(),r=vt(),n=ei(),i=mt(),a=ze(),s=function(){},o=i("Reflect","construct"),u=/^\s*(?:class|function)\b/,h=t(u.exec),c=!u.test(s),l=function(t){if(!r(t))return!1;try{return o(s,[],t),!0}catch(e){return!1}},f=function(t){if(!r(t))return!1;switch(n(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return c||!!h(u,a(t))}catch(e){return!0}};return f.sham=!0,Jr=!o||e(function(){var t;return l(l.call)||!l(Object)||!l(function(){t=!0})||t})?f:l}function ni(){if(rn)return en;rn=1;var t=ri(),e=At(),r=TypeError;return en=function(n){if(t(n))return n;throw new r(e(n)+" is not a constructor")}}function ii(){if(an)return nn;an=1;var t=we(),e=ni(),r=gt(),n=oe()("species");return nn=function(i,a){var s,o=t(i).constructor;return void 0===o||r(s=t(o)[n])?a:e(s)},nn}function ai(){if(on)return sn;on=1;var t=x(),e=Function.prototype,r=e.apply,n=e.call;return sn="object"==typeof Reflect&&Reflect.apply||(t?n.bind(r):function(){return n.apply(r,arguments)}),sn}function si(){if(hn)return un;hn=1;var t=lt(),e=ct();return un=function(r){if("Function"===t(r))return e(r)}}function oi(){if(ln)return cn;ln=1;var t=si(),e=Et(),r=x(),n=t(t.bind);return cn=function(t,i){return e(t),void 0===i?t:r?n(t,i):function(){return t.apply(i,arguments)}},cn}function ui(){if(gn)return fn;gn=1;var t=mt();return fn=t("document","documentElement")}function hi(){if(dn)return pn;dn=1;var t=ct();return pn=t([].slice)}function ci(){if(yn)return vn;yn=1;var t=TypeError;return vn=function(e,r){if(e<r)throw new t("Not enough arguments");return e}}function li(){if(xn)return mn;xn=1;var t=bt();return mn=/(?:ipad|iphone|ipod).*applewebkit/i.test(t)}function fi(){if(wn)return bn;wn=1;var t,e,r,n,i=o(),a=ai(),s=oi(),u=vt(),h=ae(),c=y(),l=ui(),f=hi(),g=he(),p=ci(),d=li(),v=Wn(),m=i.setImmediate,x=i.clearImmediate,b=i.process,w=i.Dispatch,S=i.Function,T=i.MessageChannel,O=i.String,A=0,E={},C="onreadystatechange";c(function(){t=i.location});var P=function(t){if(h(E,t)){var e=E[t];delete E[t],e()}},N=function(t){return function(){P(t)}},M=function(t){P(t.data)},R=function(e){i.postMessage(O(e),t.protocol+"//"+t.host)};return m&&x||(m=function(t){p(arguments.length,1);var r=u(t)?t:S(t),n=f(arguments,1);return E[++A]=function(){a(r,void 0,n)},e(A),A},x=function(t){delete E[t]},v?e=function(t){b.nextTick(N(t))}:w&&w.now?e=function(t){w.now(N(t))}:T&&!d?(n=(r=new T).port2,r.port1.onmessage=M,e=s(n.postMessage,n)):i.addEventListener&&u(i.postMessage)&&!i.importScripts&&t&&"file:"!==t.protocol&&!c(R)?(e=R,i.addEventListener("message",M,!1)):e=C in g("script")?function(t){l.appendChild(g("script"))[C]=function(){l.removeChild(this),P(t)}}:function(t){setTimeout(N(t),0)}),bn={set:m,clear:x}}function gi(){if(Tn)return Sn;Tn=1;var t=o(),e=m(),r=Object.getOwnPropertyDescriptor;return Sn=function(n){if(!e)return t[n];var i=r(t,n);return i&&i.value}}function pi(){if(An)return On;An=1;var t=function(){this.head=null,this.tail=null};return t.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},On=t}function di(){if(Rn)return Mn;Rn=1;var t,e,r,n,i,a=o(),s=gi(),u=oi(),h=fi().set,c=pi(),l=li(),f=function(){if(Cn)return En;Cn=1;var t=bt();return En=/ipad|iphone|ipod/i.test(t)&&"undefined"!=typeof Pebble}(),g=function(){if(Nn)return Pn;Nn=1;var t=bt();return Pn=/web0s(?!.*chrome)/i.test(t)}(),p=Wn(),d=a.MutationObserver||a.WebKitMutationObserver,v=a.document,y=a.process,m=a.Promise,x=s("queueMicrotask");if(!x){var b=new c,w=function(){var e,r;for(p&&(e=y.domain)&&e.exit();r=b.get();)try{r()}catch(n){throw b.head&&t(),n}e&&e.enter()};l||p||g||!d||!v?!f&&m&&m.resolve?((n=m.resolve(void 0)).constructor=m,i=u(n.then,n),t=function(){i(w)}):p?t=function(){y.nextTick(w)}:(h=u(h,a),t=function(){h(w)}):(e=!0,r=v.createTextNode(""),new d(w).observe(r,{characterData:!0}),t=function(){r.data=e=!e}),x=function(e){b.head||t(),b.add(e)}}return Mn=x}function vi(){return Vn||(Vn=1,_n=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(r){}}),_n}function yi(){return kn?In:(kn=1,In=function(t){try{return{error:!1,value:t()}}catch(e){return{error:!0,value:e}}})}function mi(){if(Dn)return Ln;Dn=1;var t=o();return Ln=t.Promise}function xi(){if(Bn)return jn;Bn=1;var t=o(),e=mi(),r=vt(),n=Hn(),i=ze(),a=oe(),s=Yn(),u=te(),h=wt(),c=e&&e.prototype,l=a("species"),f=!1,g=r(t.PromiseRejectionEvent),p=n("Promise",function(){var t=i(e),r=t!==String(e);if(!r&&66===h)return!0;if(u&&(!c.catch||!c.finally))return!0;if(!h||h<51||!/native code/.test(t)){var n=new e(function(t){t(1)}),a=function(t){t(function(){},function(){})};if((n.constructor={})[l]=a,!(f=n.then(function(){})instanceof a))return!0}return!(r||"BROWSER"!==s&&"DENO"!==s||g)});return jn={CONSTRUCTOR:p,REJECTION_EVENT:g,SUBCLASSING:f}}var bi,wi,Si={};function Ti(){if(bi)return Si;bi=1;var t=Et(),e=TypeError,r=function(r){var n,i;this.promise=new r(function(t,r){if(void 0!==n||void 0!==i)throw new e("Bad Promise constructor");n=t,i=r}),this.resolve=t(n),this.reject=t(i)};return Si.f=function(t){return new r(t)},Si}var Oi,Ai,Ei,Ci,Pi,Ni,Mi,Ri,_i,Vi,Ii,ki,Li,Di,ji,Bi,Fi,zi={};function Ui(){return Ai?Oi:(Ai=1,Oi={})}function Hi(){if(Ci)return Ei;Ci=1;var t=oe(),e=Ui(),r=t("iterator"),n=Array.prototype;return Ei=function(t){return void 0!==t&&(e.Array===t||n[r]===t)}}function Xi(){if(Ni)return Pi;Ni=1;var t=ei(),e=Ct(),r=gt(),n=Ui(),i=oe()("iterator");return Pi=function(a){if(!r(a))return e(a,i)||e(a,"@@iterator")||n[t(a)]}}function Yi(){if(Ri)return Mi;Ri=1;var t=b(),e=Et(),r=we(),n=At(),i=Xi(),a=TypeError;return Mi=function(s,o){var u=arguments.length<2?i(s):o;if(e(u))return r(t(u,s));throw new a(n(s)+" is not iterable")},Mi}function Wi(){if(Vi)return _i;Vi=1;var t=b(),e=we(),r=Ct();return _i=function(n,i,a){var s,o;e(n);try{if(!(s=r(n,"return"))){if("throw"===i)throw a;return a}s=t(s,n)}catch(u){o=!0,s=u}if("throw"===i)throw a;if(o)throw s;return e(s),a}}function qi(){if(ki)return Ii;ki=1;var t=oi(),e=b(),r=we(),n=At(),i=Hi(),a=pr(),s=xt(),o=Yi(),u=Xi(),h=Wi(),c=TypeError,l=function(t,e){this.stopped=t,this.result=e},f=l.prototype;return Ii=function(g,p,d){var v,y,m,x,b,w,S,T=d&&d.that,O=!(!d||!d.AS_ENTRIES),A=!(!d||!d.IS_RECORD),E=!(!d||!d.IS_ITERATOR),C=!(!d||!d.INTERRUPTED),P=t(p,T),N=function(t){return v&&h(v,"normal"),new l(!0,t)},M=function(t){return O?(r(t),C?P(t[0],t[1],N):P(t[0],t[1])):C?P(t,N):P(t)};if(A)v=g.iterator;else if(E)v=g;else{if(!(y=u(g)))throw new c(n(g)+" is not iterable");if(i(y)){for(m=0,x=a(g);x>m;m++)if((b=M(g[m]))&&s(f,b))return b;return new l(!1)}v=o(g,y)}for(w=A?g.next:v.next;!(S=e(w,v)).done;){try{b=M(S.value)}catch(R){h(v,"throw",R)}if("object"==typeof b&&b&&s(f,b))return b}return new l(!1)},Ii}function Gi(){if(Di)return Li;Di=1;var t=oe()("iterator"),e=!1;try{var r=0,n={next:function(){return{done:!!r++}},return:function(){e=!0}};n[t]=function(){return this},Array.from(n,function(){throw 2})}catch(i){}return Li=function(r,n){try{if(!n&&!e)return!1}catch(i){return!1}var a=!1;try{var s={};s[t]=function(){return{next:function(){return{done:a=!0}}}},r(s)}catch(i){}return a}}function Qi(){if(Bi)return ji;Bi=1;var t=mi(),e=Gi(),r=xi().CONSTRUCTOR;return ji=r||!e(function(e){t.all(e).then(void 0,function(){})})}var $i,Zi,Ki,Ji,ta,ea,ra,na={},ia={},aa={},sa={};function oa(){if(ta)return Ji;ta=1;var t=we(),e=yt(),r=Ti();return Ji=function(n,i){if(t(n),e(i)&&i.constructor===n)return i;var a=r.f(n);return(0,a.resolve)(i),a.promise}}function ua(t,e,r,n,i,a,s){try{var o=t[a](s),u=o.value}catch(t){return void r(t)}o.done?e(u):Promise.resolve(u).then(n,i)}function ha(t){return function(){var e=this,r=arguments;return new Promise(function(n,i){var a=t.apply(e,r);function s(t){ua(a,n,i,s,o,"next",t)}function o(t){ua(a,n,i,s,o,"throw",t)}s(void 0)})}}ra||(ra=1,function(){if(wi)return s;wi=1;var t,e,r,n,i=Xn(),a=te(),u=Wn(),h=o(),c=qn(),l=b(),f=We(),g=$n(),p=Zn(),d=Jn(),v=Et(),y=vt(),m=yt(),x=ti(),w=ii(),S=fi().set,T=di(),O=vi(),A=yi(),E=pi(),C=Xe(),P=mi(),N=xi(),M=Ti(),R="Promise",_=N.CONSTRUCTOR,V=N.REJECTION_EVENT,I=N.SUBCLASSING,k=C.getterFor(R),L=C.set,D=P&&P.prototype,j=P,B=D,F=h.TypeError,z=h.document,U=h.process,H=M.f,X=H,Y=!!(z&&z.createEvent&&h.dispatchEvent),W="unhandledrejection",q=function(t){var e;return!(!m(t)||!y(e=t.then))&&e},G=function(t,e){var r,n,i,a=e.value,s=1===e.state,o=s?t.ok:t.fail,u=t.resolve,h=t.reject,c=t.domain;try{o?(s||(2===e.rejection&&J(e),e.rejection=1),!0===o?r=a:(c&&c.enter(),r=o(a),c&&(c.exit(),i=!0)),r===t.promise?h(new F("Promise-chain cycle")):(n=q(r))?l(n,r,u,h):u(r)):h(a)}catch(f){c&&!i&&c.exit(),h(f)}},Q=function(t,e){t.notified||(t.notified=!0,T(function(){for(var r,n=t.reactions;r=n.get();)G(r,t);t.notified=!1,e&&!t.rejection&&Z(t)}))},$=function(t,e,r){var n,i;Y?((n=z.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),h.dispatchEvent(n)):n={promise:e,reason:r},!V&&(i=h["on"+t])?i(n):t===W&&O("Unhandled promise rejection",r)},Z=function(t){l(S,h,function(){var e,r=t.facade,n=t.value;if(K(t)&&(e=A(function(){u?U.emit("unhandledRejection",n,r):$(W,r,n)}),t.rejection=u||K(t)?2:1,e.error))throw e.value})},K=function(t){return 1!==t.rejection&&!t.parent},J=function(t){l(S,h,function(){var e=t.facade;u?U.emit("rejectionHandled",e):$("rejectionhandled",e,t.value)})},tt=function(t,e,r){return function(n){t(e,n,r)}},et=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,Q(t,!0))},rt=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new F("Promise can't be resolved itself");var n=q(e);n?T(function(){var r={done:!1};try{l(n,e,tt(rt,r,t),tt(et,r,t))}catch(i){et(r,i,t)}}):(t.value=e,t.state=1,Q(t,!1))}catch(i){et({done:!1},i,t)}}};if(_&&(B=(j=function(e){x(this,B),v(e),l(t,this);var r=k(this);try{e(tt(rt,r),tt(et,r))}catch(n){et(r,n)}}).prototype,(t=function(t){L(this,{type:R,done:!1,notified:!1,parent:!1,reactions:new E,rejection:!1,state:0,value:null})}).prototype=f(B,"then",function(t,e){var r=k(this),n=H(w(this,j));return r.parent=!0,n.ok=!y(t)||t,n.fail=y(e)&&e,n.domain=u?U.domain:void 0,0===r.state?r.reactions.add(n):T(function(){G(n,r)}),n.promise}),e=function(){var e=new t,r=k(e);this.promise=e,this.resolve=tt(rt,r),this.reject=tt(et,r)},M.f=H=function(t){return t===j||t===r?new e(t):X(t)},!a&&y(P)&&D!==Object.prototype)){n=D.then,I||f(D,"then",function(t,e){var r=this;return new j(function(t,e){l(n,r,t,e)}).then(t,e)},{unsafe:!0});try{delete D.constructor}catch(nt){}g&&g(D,B)}i({global:!0,constructor:!0,wrap:!0,forced:_},{Promise:j}),r=c.Promise,p(j,R,!1,!0),d(R)}(),function(){if(Fi)return zi;Fi=1;var t=Xn(),e=b(),r=Et(),n=Ti(),i=yi(),a=qi();t({target:"Promise",stat:!0,forced:Qi()},{all:function(t){var s=this,o=n.f(s),u=o.resolve,h=o.reject,c=i(function(){var n=r(s.resolve),i=[],o=0,c=1;a(t,function(t){var r=o++,a=!1;c++,e(n,s,t).then(function(t){a||(a=!0,i[r]=t,--c||u(i))},h)}),--c||u(i)});return c.error&&h(c.value),o.promise}})}(),function(){if($i)return na;$i=1;var t=Xn(),e=te(),r=xi().CONSTRUCTOR,n=mi(),i=mt(),a=vt(),s=We(),o=n&&n.prototype;if(t({target:"Promise",proto:!0,forced:r,real:!0},{catch:function(t){return this.then(void 0,t)}}),!e&&a(n)){var u=i("Promise").prototype.catch;o.catch!==u&&s(o,"catch",u,{unsafe:!0})}}(),function(){if(Zi)return ia;Zi=1;var t=Xn(),e=b(),r=Et(),n=Ti(),i=yi(),a=qi();t({target:"Promise",stat:!0,forced:Qi()},{race:function(t){var s=this,o=n.f(s),u=o.reject,h=i(function(){var n=r(s.resolve);a(t,function(t){e(n,s,t).then(o.resolve,u)})});return h.error&&u(h.value),o.promise}})}(),function(){if(Ki)return aa;Ki=1;var t=Xn(),e=Ti();t({target:"Promise",stat:!0,forced:xi().CONSTRUCTOR},{reject:function(t){var r=e.f(this);return(0,r.reject)(t),r.promise}})}(),function(){if(ea)return sa;ea=1;var t=Xn(),e=mt(),r=te(),n=mi(),i=xi().CONSTRUCTOR,a=oa(),s=e("Promise"),o=r&&!i;t({target:"Promise",stat:!0,forced:r||i},{resolve:function(t){return a(o&&this===s?n:this,t)}})}());var ca,la,fa,ga,pa,da,va={},ya={};function ma(){if(la)return ca;la=1;var t=ei(),e=String;return ca=function(r){if("Symbol"===t(r))throw new TypeError("Cannot convert a Symbol value to a string");return e(r)}}function xa(){if(ga)return fa;ga=1;var t=we();return fa=function(){var e=t(this),r="";return e.hasIndices&&(r+="d"),e.global&&(r+="g"),e.ignoreCase&&(r+="i"),e.multiline&&(r+="m"),e.dotAll&&(r+="s"),e.unicode&&(r+="u"),e.unicodeSets&&(r+="v"),e.sticky&&(r+="y"),r}}function ba(){if(da)return pa;da=1;var t=y(),e=o().RegExp,r=t(function(){var t=e("a","y");return t.lastIndex=2,null!==t.exec("abcd")}),n=r||t(function(){return!e("a","y").sticky}),i=r||t(function(){var t=e("^r","gy");return t.lastIndex=2,null!==t.exec("str")});return pa={BROKEN_CARET:i,MISSED_STICKY:n,UNSUPPORTED_Y:r}}var wa,Sa,Ta,Oa,Aa,Ea,Ca,Pa,Na,Ma,Ra,_a,Va,Ia,ka,La,Da,ja,Ba,Fa,za,Ua,Ha,Xa,Ya,Wa={};function qa(){if(Sa)return wa;Sa=1;var t=vr(),e=yr();return wa=Object.keys||function(r){return t(r,e)},wa}function Ga(){if(Aa)return Oa;Aa=1;var t,e=we(),r=function(){if(Ta)return Wa;Ta=1;var t=m(),e=be(),r=Se(),n=we(),i=dt(),a=qa();return Wa.f=t&&!e?Object.defineProperties:function(t,e){n(t);for(var s,o=i(e),u=a(e),h=u.length,c=0;h>c;)r.f(t,s=u[c++],o[s]);return t},Wa}(),n=yr(),i=He(),a=ui(),s=he(),o=Ue(),u="prototype",h="script",c=o("IE_PROTO"),l=function(){},f=function(t){return"<"+h+">"+t+"</"+h+">"},g=function(t){t.write(f("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{t=new ActiveXObject("htmlfile")}catch(c){}var e,r,i;p="undefined"!=typeof document?document.domain&&t?g(t):(r=s("iframe"),i="java"+h+":",r.style.display="none",a.appendChild(r),r.src=String(i),(e=r.contentWindow.document).open(),e.write(f("document.F=Object")),e.close(),e.F):g(t);for(var o=n.length;o--;)delete p[u][n[o]];return p()};return i[c]=!0,Oa=Object.create||function(t,n){var i;return null!==t?(l[u]=e(t),i=new l,l[u]=null,i[c]=t):i=p(),void 0===n?i:r.f(i,n)},Oa}function Qa(){if(Ra)return Ma;Ra=1;var t,e,r=b(),n=ct(),i=ma(),a=xa(),s=ba(),u=ne(),h=Ga(),c=Xe().get,l=function(){if(Ca)return Ea;Ca=1;var t=y(),e=o().RegExp;return Ea=t(function(){var t=e(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})}(),f=function(){if(Na)return Pa;Na=1;var t=y(),e=o().RegExp;return Pa=t(function(){var t=e("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})}(),g=u("native-string-replace",String.prototype.replace),p=RegExp.prototype.exec,d=p,v=n("".charAt),m=n("".indexOf),x=n("".replace),w=n("".slice),S=(e=/b*/g,r(p,t=/a/,"a"),r(p,e,"a"),0!==t.lastIndex||0!==e.lastIndex),T=s.BROKEN_CARET,O=void 0!==/()??/.exec("")[1];return(S||O||T||l||f)&&(d=function(t){var e,n,s,o,u,l,f,y=this,b=c(y),A=i(t),E=b.raw;if(E)return E.lastIndex=y.lastIndex,e=r(d,E,A),y.lastIndex=E.lastIndex,e;var C=b.groups,P=T&&y.sticky,N=r(a,y),M=y.source,R=0,_=A;if(P&&(N=x(N,"y",""),-1===m(N,"g")&&(N+="g"),_=w(A,y.lastIndex),y.lastIndex>0&&(!y.multiline||y.multiline&&"\n"!==v(A,y.lastIndex-1))&&(M="(?: "+M+")",_=" "+_,R++),n=new RegExp("^(?:"+M+")",N)),O&&(n=new RegExp("^"+M+"$(?!\\s)",N)),S&&(s=y.lastIndex),o=r(p,P?n:y,_),P?o?(o.input=w(o.input,R),o[0]=w(o[0],R),o.index=y.lastIndex,y.lastIndex+=o[0].length):y.lastIndex=0:S&&o&&(y.lastIndex=y.global?o.index+o[0].length:s),O&&o&&o.length>1&&r(g,o[0],n,function(){for(u=1;u<arguments.length-2;u++)void 0===arguments[u]&&(o[u]=void 0)}),o&&C)for(o.groups=l=h(null),u=0;u<C.length;u++)l[(f=C[u])[0]]=o[f[1]];return o}),Ma=d}function $a(){if(Ia)return Va;Ia=1,function(){if(_a)return ya;_a=1;var t=Xn(),e=Qa();t({target:"RegExp",proto:!0,forced:/./.exec!==e},{exec:e})}();var t=b(),e=We(),r=Qa(),n=y(),i=oe(),a=Te(),s=i("species"),o=RegExp.prototype;return Va=function(u,h,c,l){var f=i(u),g=!n(function(){var t={};return t[f]=function(){return 7},7!==""[u](t)}),p=g&&!n(function(){var t=!1,e=/a/;return"split"===u&&((e={}).constructor={},e.constructor[s]=function(){return e},e.flags="",e[f]=/./[f]),e.exec=function(){return t=!0,null},e[f](""),!t});if(!g||!p||c){var d=/./[f],v=h(f,""[u],function(e,n,i,a,s){var u=n.exec;return u===r||u===o.exec?g&&!s?{done:!0,value:t(d,n,i,a)}:{done:!0,value:t(e,i,n,a)}:{done:!1}});e(String.prototype,u,v[0]),e(o,f,v[1])}l&&a(o[f],"sham",!0)},Va}function Za(){if(ja)return Da;ja=1;var t=function(){if(La)return ka;La=1;var t=ct(),e=lr(),r=ma(),n=pt(),i=t("".charAt),a=t("".charCodeAt),s=t("".slice),o=function(t){return function(o,u){var h,c,l=r(n(o)),f=e(u),g=l.length;return f<0||f>=g?t?"":void 0:(h=a(l,f))<55296||h>56319||f+1===g||(c=a(l,f+1))<56320||c>57343?t?i(l,f):h:t?s(l,f,f+2):c-56320+(h-55296<<10)+65536}};return ka={codeAt:o(!1),charAt:o(!0)}}().charAt;return Da=function(e,r,n){return r+(n?t(e,r).length:1)},Da}function Ka(){if(Ua)return za;Ua=1;var t=b(),e=ae(),r=xt(),n=function(){if(Fa)return Ba;Fa=1;var t=o(),e=y(),r=t.RegExp,n=!e(function(){var t=!0;try{r(".","d")}catch(u){t=!1}var e={},n="",i=t?"dgimsy":"gimsy",a=function(t,r){Object.defineProperty(e,t,{get:function(){return n+=r,!0}})},s={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var o in t&&(s.hasIndices="d"),s)a(o,s[o]);return Object.getOwnPropertyDescriptor(r.prototype,"flags").get.call(e)!==i||n!==i});return Ba={correct:n}}(),i=xa(),a=RegExp.prototype;return za=n.correct?function(t){return t.flags}:function(s){return n.correct||!r(a,s)||e(s,"flags")?s.flags:t(i,s)}}function Ja(){if(Xa)return Ha;Xa=1;var t=b(),e=we(),r=vt(),n=lt(),i=Qa(),a=TypeError;return Ha=function(s,o){var u=s.exec;if(r(u)){var h=t(u,s,o);return null!==h&&e(h),h}if("RegExp"===n(s))return t(i,s,o);throw new a("RegExp#exec called on incompatible receiver")}}!function(){if(Ya)return va;Ya=1;var t=b(),e=ct(),r=$a(),n=we(),i=yt(),a=gr(),s=ma(),o=pt(),u=Ct(),h=Za(),c=Ka(),l=Ja(),f=e("".indexOf);r("match",function(e,r,g){return[function(r){var n=o(this),a=i(r)?u(r,e):void 0;return a?t(a,r,n):new RegExp(r)[e](s(n))},function(t){var e=n(this),i=s(t),o=g(r,e,i);if(o.done)return o.value;var u=s(c(e));if(-1===f(u,"g"))return l(e,i);var p=-1!==f(u,"u");e.lastIndex=0;for(var d,v=[],y=0;null!==(d=l(e,i));){var m=s(d[0]);v[y]=m,""===m&&(e.lastIndex=h(i,a(e.lastIndex),p)),y++}return 0===y?null:v}]})}();var ts,es,rs,ns={};function is(){if(es)return ts;es=1;var t=ct(),e=ie(),r=Math.floor,n=t("".charAt),i=t("".replace),a=t("".slice),s=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,o=/\$([$&'`]|\d{1,2})/g;return ts=function(t,u,h,c,l,f){var g=h+t.length,p=c.length,d=o;return void 0!==l&&(l=e(l),d=s),i(f,d,function(e,i){var s;switch(n(i,0)){case"$":return"$";case"&":return t;case"`":return a(u,0,h);case"'":return a(u,g);case"<":s=l[a(i,1,-1)];break;default:var o=+i;if(0===o)return e;if(o>p){var f=r(o/10);return 0===f?e:f<=p?void 0===c[f-1]?n(i,1):c[f-1]+n(i,1):e}s=c[o-1]}return void 0===s?"":s})},ts}!function(){if(rs)return ns;rs=1;var t=ai(),e=b(),r=ct(),n=$a(),i=y(),a=we(),s=vt(),o=yt(),u=lr(),h=gr(),c=ma(),l=pt(),f=Za(),g=Ct(),p=is(),d=Ka(),v=Ja(),m=oe()("replace"),x=Math.max,w=Math.min,S=r([].concat),T=r([].push),O=r("".indexOf),A=r("".slice),E=function(t){return void 0===t?t:String(t)},C="$0"==="a".replace(/./,"$0"),P=!!/./[m]&&""===/./[m]("a","$0");n("replace",function(r,n,i){var y=P?"$":"$0";return[function(t,r){var i=l(this),a=o(t)?g(t,m):void 0;return a?e(a,t,i,r):e(n,c(i),t,r)},function(e,r){var o=a(this),l=c(e);if("string"==typeof r&&-1===O(r,y)&&-1===O(r,"$<")){var g=i(n,o,l,r);if(g.done)return g.value}var m=s(r);m||(r=c(r));var b,C=c(d(o)),P=-1!==O(C,"g");P&&(b=-1!==O(C,"u"),o.lastIndex=0);for(var N,M=[];null!==(N=v(o,l))&&(T(M,N),P);)""===c(N[0])&&(o.lastIndex=f(l,h(o.lastIndex),b));for(var R="",_=0,V=0;V<M.length;V++){for(var I,k=c((N=M[V])[0]),L=x(w(u(N.index),l.length),0),D=[],j=1;j<N.length;j++)T(D,E(N[j]));var B=N.groups;if(m){var F=S([k],D,L,l);void 0!==B&&T(F,B),I=c(t(r,void 0,F))}else I=p(k,l,L,D,B,r);L>=_&&(R+=A(l,_,L)+I,_=L+k.length)}return R+A(l,_)}]},!!i(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})||!C||P)}();var as,ss,os,us,hs,cs,ls,fs,gs,ps,ds,vs,ys,ms,xs,bs,ws,Ss,Ts,Os,As,Es,Cs,Ps={};function Ns(){if(us)return os;us=1;var t=function(){if(ss)return as;ss=1;var t=yt(),e=lt(),r=oe()("match");return as=function(n){var i;return t(n)&&(void 0!==(i=n[r])?!!i:"RegExp"===e(n))}}(),e=TypeError;return os=function(r){if(t(r))throw new e("The method doesn't accept regular expressions");return r}}function Ms(){if(cs)return hs;cs=1;var t=oe()("match");return hs=function(e){var r=/./;try{"/./"[e](r)}catch(n){try{return r[t]=!1,"/./"[e](r)}catch(i){}}return!1}}function Rs(){if(gs)return fs;gs=1;var t=oe(),e=Ga(),r=Se().f,n=t("unscopables"),i=Array.prototype;return void 0===i[n]&&r(i,n,{configurable:!0,value:e(null)}),fs=function(t){i[n][t]=!0}}function _s(){if(ys)return vs;ys=1;var t=ae(),e=vt(),r=ie(),n=Ue(),i=function(){if(ds)return ps;ds=1;var t=y();return ps=!t(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})}(),a=n("IE_PROTO"),s=Object,o=s.prototype;return vs=i?s.getPrototypeOf:function(n){var i=r(n);if(t(i,a))return i[a];var u=i.constructor;return e(u)&&i instanceof u?u.prototype:i instanceof s?o:null},vs}function Vs(){if(xs)return ms;xs=1;var t,e,r,n=y(),i=vt(),a=yt(),s=Ga(),o=_s(),u=We(),h=oe(),c=te(),l=h("iterator"),f=!1;return[].keys&&("next"in(r=[].keys())?(e=o(o(r)))!==Object.prototype&&(t=e):f=!0),!a(t)||n(function(){var e={};return t[l].call(e)!==e})?t={}:c&&(t=s(t)),i(t[l])||u(t,l,function(){return this}),ms={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:f}}function Is(){if(Ts)return Ss;Ts=1;var t=Xn(),e=b(),r=te(),n=Fe(),i=vt(),a=function(){if(ws)return bs;ws=1;var t=Vs().IteratorPrototype,e=Ga(),r=ht(),n=Zn(),i=Ui(),a=function(){return this};return bs=function(s,o,u,h){var c=o+" Iterator";return s.prototype=e(t,{next:r(+!h,u)}),n(s,c,!1,!0),i[c]=a,s}}(),s=_s(),o=$n(),u=Zn(),h=Te(),c=We(),l=oe(),f=Ui(),g=Vs(),p=n.PROPER,d=n.CONFIGURABLE,v=g.IteratorPrototype,y=g.BUGGY_SAFARI_ITERATORS,m=l("iterator"),x="keys",w="values",S="entries",T=function(){return this};return Ss=function(n,l,g,b,O,A,E){a(g,l,b);var C,P,N,M=function(t){if(t===O&&k)return k;if(!y&&t&&t in V)return V[t];switch(t){case x:case w:case S:return function(){return new g(this,t)}}return function(){return new g(this)}},R=l+" Iterator",_=!1,V=n.prototype,I=V[m]||V["@@iterator"]||O&&V[O],k=!y&&I||M(O),L="Array"===l&&V.entries||I;if(L&&(C=s(L.call(new n)))!==Object.prototype&&C.next&&(r||s(C)===v||(o?o(C,v):i(C[m])||c(C,m,T)),u(C,R,!0,!0),r&&(f[R]=T)),p&&O===w&&I&&I.name!==w&&(!r&&d?h(V,"name",w):(_=!0,k=function(){return e(I,this)})),O)if(P={values:M(w),keys:A?k:M(x),entries:M(S)},E)for(N in P)(y||_||!(N in V))&&c(V,N,P[N]);else t({target:l,proto:!0,forced:y||_},P);return r&&!E||V[m]===k||c(V,m,k,{name:O}),f[l]=k,P}}function ks(){return As?Os:(As=1,Os=function(t,e){return{value:t,done:e}})}function Ls(){if(Cs)return Es;Cs=1;var t=dt(),e=Rs(),r=Ui(),n=Xe(),i=Se().f,a=Is(),s=ks(),o=te(),u=m(),h="Array Iterator",c=n.set,l=n.getterFor(h);Es=a(Array,"Array",function(e,r){c(this,{type:h,target:t(e),index:0,kind:r})},function(){var t=l(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,s(void 0,!0);switch(t.kind){case"keys":return s(r,!1);case"values":return s(e[r],!1)}return s([r,e[r]],!1)},"values");var f=r.Arguments=r.Array;if(e("keys"),e("values"),e("entries"),!o&&u&&"values"!==f.name)try{i(f,"name",{value:"values"})}catch(g){}return Es}!function(){if(ls)return Ps;ls=1;var t,e=Xn(),r=si(),n=le().f,i=gr(),a=ma(),s=Ns(),o=pt(),u=Ms(),h=te(),c=r("".slice),l=Math.min,f=u("startsWith");e({target:"String",proto:!0,forced:!(!h&&!f&&(t=n(String.prototype,"startsWith"),t&&!t.writable)||f)},{startsWith:function(t){var e=a(o(this));s(t);var r=i(l(arguments.length>1?arguments[1]:void 0,e.length)),n=a(t);return c(e,r,r+n.length)===n}})}(),Ls();var Ds,js,Bs,Fs,zs,Us={};function Hs(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e);if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}function Xs(t,e,r){return(e=Hs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(){if(zs)return Us;zs=1;var t=o(),e=js?Ds:(js=1,Ds={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}),r=function(){if(Fs)return Bs;Fs=1;var t=he()("span").classList,e=t&&t.constructor&&t.constructor.prototype;return Bs=e===Object.prototype?void 0:e}(),n=Ls(),i=Te(),a=Zn(),s=oe()("iterator"),u=n.values,h=function(t,r){if(t){if(t[s]!==u)try{i(t,s,u)}catch(h){t[s]=u}if(a(t,r,!0),e[r])for(var o in n)if(t[o]!==n[o])try{i(t,o,n[o])}catch(h){t[o]=n[o]}}};for(var c in e)h(t[c]&&t[c].prototype,c);h(r,"DOMTokenList")}();var Ys,Ws,qs,Gs,Qs,$s={};function Zs(){if(Gs)return qs;Gs=1;var t=y();return qs=function(e,r){var n=[][e];return!!n&&t(function(){n.call(null,r||function(){return 1},1)})}}!function(){if(Qs)return $s;Qs=1;var t=Xn(),e=function(){if(Ws)return Ys;Ws=1;var t=Et(),e=ie(),r=ft(),n=pr(),i=TypeError,a="Reduce of empty array with no initial value",s=function(s){return function(o,u,h,c){var l=e(o),f=r(l),g=n(l);if(t(u),0===g&&h<2)throw new i(a);var p=s?g-1:0,d=s?-1:1;if(h<2)for(;;){if(p in f){c=f[p],p+=d;break}if(p+=d,s?p<0:g<=p)throw new i(a)}for(;s?p>=0:g>p;p+=d)p in f&&(c=u(c,f[p],p,l));return c}};return Ys={left:s(!1),right:s(!0)}}().left,r=Zs(),n=wt();t({target:"Array",proto:!0,forced:!Wn()&&n>79&&n<83||!r("reduce")},{reduce:function(t){var r=arguments.length;return e(this,t,r,r>1?arguments[1]:void 0)}})}();var Ks,Js={};!function(){if(Ks)return Js;Ks=1;var t,e=Xn(),r=si(),n=le().f,i=gr(),a=ma(),s=Ns(),o=pt(),u=Ms(),h=te(),c=r("".slice),l=Math.min,f=u("endsWith");e({target:"String",proto:!0,forced:!(!h&&!f&&(t=n(String.prototype,"endsWith"),t&&!t.writable)||f)},{endsWith:function(t){var e=a(o(this));s(t);var r=arguments.length>1?arguments[1]:void 0,n=e.length,u=void 0===r?n:l(i(r),n),h=a(t);return c(e,u-h.length,u)===h}})}();var to,eo={};!function(){if(to)return eo;to=1;var t=b(),e=ct(),r=$a(),n=we(),i=yt(),a=pt(),s=ii(),o=Za(),u=gr(),h=ma(),c=Ct(),l=Ja(),f=ba(),g=y(),p=f.UNSUPPORTED_Y,d=Math.min,v=e([].push),m=e("".slice),x=!g(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]}),w="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;r("split",function(e,r,f){var g="0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t(r,this,e,n)}:r;return[function(r,n){var s=a(this),o=i(r)?c(r,e):void 0;return o?t(o,r,s,n):t(g,h(s),r,n)},function(t,e){var i=n(this),a=h(t);if(!w){var c=f(g,i,a,e,g!==r);if(c.done)return c.value}var y=s(i,RegExp),x=i.unicode,b=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(p?"g":"y"),S=new y(p?"^(?:"+i.source+")":i,b),T=void 0===e?4294967295:e>>>0;if(0===T)return[];if(0===a.length)return null===l(S,a)?[a]:[];for(var O=0,A=0,E=[];A<a.length;){S.lastIndex=p?0:A;var C,P=l(S,p?m(a,A):a);if(null===P||(C=d(u(S.lastIndex+(p?A:0)),a.length))===O)A=o(a,A,x);else{if(v(E,m(a,O,A)),E.length===T)return E;for(var N=1;N<=P.length-1;N++)if(v(E,P[N]),E.length===T)return E;A=O=C}}return v(E,m(a,O)),E}]},w||!x,p)}();var ro,no,io={exports:{}},ao={exports:{}},so=ao.exports,oo=function(){if(no)return io.exports;no=1;for(var t=(ro||(ro=1,function(){var t,e,r,n,i,a;"undefined"!=typeof performance&&null!==performance&&performance.now?ao.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(ao.exports=function(){return(t()-i)/1e6},e=process.hrtime,n=(t=function(){var t;return 1e9*(t=e())[0]+t[1]})(),a=1e9*process.uptime(),i=n-a):Date.now?(ao.exports=function(){return Date.now()-r},r=Date.now()):(ao.exports=function(){return(new Date).getTime()-r},r=(new Date).getTime())}.call(so)),ao.exports),r="undefined"==typeof window?e:window,n=["moz","webkit"],i="AnimationFrame",a=r["request"+i],s=r["cancel"+i]||r["cancelRequest"+i],o=0;!a&&o<n.length;o++)a=r[n[o]+"Request"+i],s=r[n[o]+"Cancel"+i]||r[n[o]+"CancelRequest"+i];if(!a||!s){var u=0,h=0,c=[],l=1e3/60;a=function(e){if(0===c.length){var r=t(),n=Math.max(0,l-(r-u));u=n+r,setTimeout(function(){var t=c.slice(0);c.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(u)}catch(r){setTimeout(function(){throw r},0)}},Math.round(n))}return c.push({handle:++h,callback:e,cancelled:!1}),h},s=function(t){for(var e=0;e<c.length;e++)c[e].handle===t&&(c[e].cancelled=!0)}}return io.exports=function(t){return a.call(r,t)},io.exports.cancel=function(){s.apply(r,arguments)},io.exports.polyfill=function(t){t||(t=r),t.requestAnimationFrame=a,t.cancelAnimationFrame=s},io.exports}();const uo=n(oo);var ho,co,lo,fo,go,po,vo,yo,mo,xo={};function bo(){return co?ho:(co=1,ho="\t\n\v\f\r                　\u2028\u2029\ufeff")}!function(){if(vo)return xo;vo=1;var t=Xn(),e=function(){if(fo)return lo;fo=1;var t=ct(),e=pt(),r=ma(),n=bo(),i=t("".replace),a=RegExp("^["+n+"]+"),s=RegExp("(^|[^"+n+"])["+n+"]+$"),o=function(t){return function(n){var o=r(e(n));return 1&t&&(o=i(o,a,"")),2&t&&(o=i(o,s,"$1")),o}};return lo={start:o(1),end:o(2),trim:o(3)}}().trim,r=function(){if(po)return go;po=1;var t=Fe().PROPER,e=y(),r=bo();return go=function(n){return e(function(){return!!r[n]()||"​᠎"!=="​᠎"[n]()||t&&r[n].name!==n})}}();t({target:"String",proto:!0,forced:r("trim")},{trim:function(){return e(this)}})}();var wo=(mo||(mo=1,yo=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],n=0;n<r.length;n++){var i=r[n].re,a=r[n].process,s=i.exec(t);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,n=0;n<r.length;n++)for(var i=r[n].example,a=0;a<i.length;a++)t[t.length]=i[a];for(var s in e)t[t.length]=s;var o=document.createElement("ul");for(o.setAttribute("id","rgbcolor-examples"),n=0;n<t.length;n++)try{var u=document.createElement("li"),h=new RGBColor(t[n]),c=document.createElement("div");c.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),c.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[n]+" -> "+h.toRGB()+" -> "+h.toHex());u.appendChild(c),u.appendChild(l),o.appendChild(u)}catch(f){}return o}}),yo);const So=n(wo);var To,Oo={};!function(){if(To)return Oo;To=1;var t=Xn(),e=si(),r=dr().indexOf,n=Zs(),i=e([].indexOf),a=!!i&&1/i([1],1,-0)<0;t({target:"Array",proto:!0,forced:a||!n("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return a?i(this,t,e)||0:r(this,t,e)}})}();var Ao,Eo={};!function(){if(Ao)return Eo;Ao=1;var t=Xn(),e=ct(),r=Ns(),n=pt(),i=ma(),a=Ms(),s=e("".indexOf);t({target:"String",proto:!0,forced:!a("includes")},{includes:function(t){return!!~s(i(n(this)),i(r(t)),arguments.length>1?arguments[1]:void 0)}})}();var Co,Po,No,Mo={};function Ro(){if(Po)return Co;Po=1;var t=lt();return Co=Array.isArray||function(e){return"Array"===t(e)}}!function(){if(No)return Mo;No=1;var t=Xn(),e=ct(),r=Ro(),n=e([].reverse),i=[1,2];t({target:"Array",proto:!0,forced:String(i)===String(i.reverse())},{reverse:function(){return r(this)&&(this.length=this.length),n(this)}})}();
/*! *****************************************************************************
			Copyright (c) Microsoft Corporation.

			Permission to use, copy, modify, and/or distribute this software for any
			purpose with or without fee is hereby granted.

			THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
			REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
			AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
			INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
			LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
			OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
			PERFORMANCE OF THIS SOFTWARE.
			***************************************************************************** */
var _o=function(t,e){return(_o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function Vo(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}_o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function Io(t,e){var r=t[0],n=t[1];return[r*Math.cos(e)-n*Math.sin(e),r*Math.sin(e)+n*Math.cos(e)]}function ko(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var Lo=Math.PI;function Do(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var n=t.rX,i=t.rY,a=t.x,s=t.y;n=Math.abs(t.rX),i=Math.abs(t.rY);var o=Io([(e-a)/2,(r-s)/2],-t.xRot/180*Lo),u=o[0],h=o[1],c=Math.pow(u,2)/Math.pow(n,2)+Math.pow(h,2)/Math.pow(i,2);1<c&&(n*=Math.sqrt(c),i*=Math.sqrt(c)),t.rX=n,t.rY=i;var l=Math.pow(n,2)*Math.pow(h,2)+Math.pow(i,2)*Math.pow(u,2),f=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(n,2)*Math.pow(i,2)-l)/l)),g=n*h/i*f,p=-i*u/n*f,d=Io([g,p],t.xRot/180*Lo);t.cX=d[0]+(e+a)/2,t.cY=d[1]+(r+s)/2,t.phi1=Math.atan2((h-p)/i,(u-g)/n),t.phi2=Math.atan2((-h-p)/i,(-u-g)/n),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*Lo),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*Lo),t.phi1*=180/Lo,t.phi2*=180/Lo}function jo(t,e,r){ko(t,e,r);var n=t*t+e*e-r*r;if(0>n)return[];if(0===n)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var i=Math.sqrt(n);return[[(t*r+e*i)/(t*t+e*e),(e*r-t*i)/(t*t+e*e)],[(t*r-e*i)/(t*t+e*e),(e*r+t*i)/(t*t+e*e)]]}var Bo,Fo=Math.PI/180;function zo(t,e,r){return(1-r)*t+r*e}function Uo(t,e,r,n){return t+Math.cos(n/180*Lo)*e+Math.sin(n/180*Lo)*r}function Ho(t,e,r,n){var i=1e-6,a=e-t,s=r-e,o=3*a+3*(n-r)-6*s,u=6*(s-a),h=3*a;return Math.abs(o)<i?[-h/u]:function(t,e,r){var n=t*t/4-e;if(n<-r)return[];if(n<=r)return[-t/2];var i=Math.sqrt(n);return[-t/2-i,-t/2+i]}(u/o,h/o,i)}function Xo(t,e,r,n,i){var a=1-i;return t*(a*a*a)+e*(3*a*a*i)+r*(3*a*i*i)+n*(i*i*i)}!function(t){function e(){return i(function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t})}function r(){var t=NaN,e=NaN,r=NaN,n=NaN;return i(function(i,a,s){return i.type&Zo.SMOOTH_CURVE_TO&&(i.type=Zo.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?s:e,i.x1=i.relative?a-t:2*a-t,i.y1=i.relative?s-e:2*s-e),i.type&Zo.CURVE_TO?(t=i.relative?a+i.x2:i.x2,e=i.relative?s+i.y2:i.y2):(t=NaN,e=NaN),i.type&Zo.SMOOTH_QUAD_TO&&(i.type=Zo.QUAD_TO,r=isNaN(r)?a:r,n=isNaN(n)?s:n,i.x1=i.relative?a-r:2*a-r,i.y1=i.relative?s-n:2*s-n),i.type&Zo.QUAD_TO?(r=i.relative?a+i.x1:i.x1,n=i.relative?s+i.y1:i.y1):(r=NaN,n=NaN),i})}function n(){var t=NaN,e=NaN;return i(function(r,n,i){if(r.type&Zo.SMOOTH_QUAD_TO&&(r.type=Zo.QUAD_TO,t=isNaN(t)?n:t,e=isNaN(e)?i:e,r.x1=r.relative?n-t:2*n-t,r.y1=r.relative?i-e:2*i-e),r.type&Zo.QUAD_TO){t=r.relative?n+r.x1:r.x1,e=r.relative?i+r.y1:r.y1;var a=r.x1,s=r.y1;r.type=Zo.CURVE_TO,r.x1=((r.relative?0:n)+2*a)/3,r.y1=((r.relative?0:i)+2*s)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*s)/3}else t=NaN,e=NaN;return r})}function i(t){var e=0,r=0,n=NaN,i=NaN;return function(a){if(isNaN(n)&&!(a.type&Zo.MOVE_TO))throw new Error("path must start with moveto");var s=t(a,e,r,n,i);return a.type&Zo.CLOSE_PATH&&(e=n,r=i),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&Zo.MOVE_TO&&(n=e,i=r),s}}function a(t,e,r,n,a,s){return ko(t,e,r,n,a,s),i(function(i,o,u,h){var c=i.x1,l=i.x2,f=i.relative&&!isNaN(h),g=void 0!==i.x?i.x:f?0:o,p=void 0!==i.y?i.y:f?0:u;function d(t){return t*t}i.type&Zo.HORIZ_LINE_TO&&0!==e&&(i.type=Zo.LINE_TO,i.y=i.relative?0:u),i.type&Zo.VERT_LINE_TO&&0!==r&&(i.type=Zo.LINE_TO,i.x=i.relative?0:o),void 0!==i.x&&(i.x=i.x*t+p*r+(f?0:a)),void 0!==i.y&&(i.y=g*e+i.y*n+(f?0:s)),void 0!==i.x1&&(i.x1=i.x1*t+i.y1*r+(f?0:a)),void 0!==i.y1&&(i.y1=c*e+i.y1*n+(f?0:s)),void 0!==i.x2&&(i.x2=i.x2*t+i.y2*r+(f?0:a)),void 0!==i.y2&&(i.y2=l*e+i.y2*n+(f?0:s));var v=t*n-e*r;if(void 0!==i.xRot&&(1!==t||0!==e||0!==r||1!==n))if(0===v)delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag,i.type=Zo.LINE_TO;else{var y=i.xRot*Math.PI/180,m=Math.sin(y),x=Math.cos(y),b=1/d(i.rX),w=1/d(i.rY),S=d(x)*b+d(m)*w,T=2*m*x*(b-w),O=d(m)*b+d(x)*w,A=S*n*n-T*e*n+O*e*e,E=T*(t*n+e*r)-2*(S*r*n+O*t*e),C=S*r*r-T*t*r+O*t*t,P=(Math.atan2(E,A-C)+Math.PI)%Math.PI/2,N=Math.sin(P),M=Math.cos(P);i.rX=Math.abs(v)/Math.sqrt(A*d(M)+E*N*M+C*d(N)),i.rY=Math.abs(v)/Math.sqrt(A*d(N)-E*N*M+C*d(M)),i.xRot=180*P/Math.PI}return void 0!==i.sweepFlag&&0>v&&(i.sweepFlag=+!i.sweepFlag),i})}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),ko(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return i(function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t})},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),i(function(n,i,a,s,o){if(isNaN(s)&&!(n.type&Zo.MOVE_TO))throw new Error("path must start with moveto");return e&&n.type&Zo.HORIZ_LINE_TO&&(n.type=Zo.LINE_TO,n.y=n.relative?0:a),r&&n.type&Zo.VERT_LINE_TO&&(n.type=Zo.LINE_TO,n.x=n.relative?0:i),t&&n.type&Zo.CLOSE_PATH&&(n.type=Zo.LINE_TO,n.x=n.relative?s-i:s,n.y=n.relative?o-a:o),n.type&Zo.ARC&&(0===n.rX||0===n.rY)&&(n.type=Zo.LINE_TO,delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag),n})},t.NORMALIZE_ST=r,t.QT_TO_C=n,t.INFO=i,t.SANITIZE=function(t){void 0===t&&(t=0),ko(t);var e=NaN,r=NaN,n=NaN,a=NaN;return i(function(i,s,o,u,h){var c=Math.abs,l=!1,f=0,g=0;if(i.type&Zo.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:s-e,g=isNaN(r)?0:o-r),i.type&(Zo.CURVE_TO|Zo.SMOOTH_CURVE_TO)?(e=i.relative?s+i.x2:i.x2,r=i.relative?o+i.y2:i.y2):(e=NaN,r=NaN),i.type&Zo.SMOOTH_QUAD_TO?(n=isNaN(n)?s:2*s-n,a=isNaN(a)?o:2*o-a):i.type&Zo.QUAD_TO?(n=i.relative?s+i.x1:i.x1,a=i.relative?o+i.y1:i.y2):(n=NaN,a=NaN),i.type&Zo.LINE_COMMANDS||i.type&Zo.ARC&&(0===i.rX||0===i.rY||!i.lArcFlag)||i.type&Zo.CURVE_TO||i.type&Zo.SMOOTH_CURVE_TO||i.type&Zo.QUAD_TO||i.type&Zo.SMOOTH_QUAD_TO){var p=void 0===i.x?0:i.relative?i.x:i.x-s,d=void 0===i.y?0:i.relative?i.y:i.y-o;f=isNaN(n)?void 0===i.x1?f:i.relative?i.x:i.x1-s:n-s,g=isNaN(a)?void 0===i.y1?g:i.relative?i.y:i.y1-o:a-o;var v=void 0===i.x2?0:i.relative?i.x:i.x2-s,y=void 0===i.y2?0:i.relative?i.y:i.y2-o;c(p)<=t&&c(d)<=t&&c(f)<=t&&c(g)<=t&&c(v)<=t&&c(y)<=t&&(l=!0)}return i.type&Zo.CLOSE_PATH&&c(s-u)<=t&&c(o-h)<=t&&(l=!0),l?[]:i})},t.MATRIX=a,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),ko(t,e,r);var n=Math.sin(t),i=Math.cos(t);return a(i,n,-n,i,e-e*i+r*n,r-e*n-r*i)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),ko(t,e),a(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),ko(t,e),a(t,0,0,e,0,0)},t.SKEW_X=function(t){return ko(t),a(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return ko(t),a(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),ko(t),a(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),ko(t),a(1,0,0,-1,0,t)},t.A_TO_C=function(){return i(function(t,e,r){return Zo.ARC===t.type?function(t,e,r){var n,i,a,s;t.cX||Do(t,e,r);for(var o=Math.min(t.phi1,t.phi2),u=Math.max(t.phi1,t.phi2)-o,h=Math.ceil(u/90),c=new Array(h),l=e,f=r,g=0;g<h;g++){var p=zo(t.phi1,t.phi2,g/h),d=zo(t.phi1,t.phi2,(g+1)/h),v=d-p,y=4/3*Math.tan(v*Fo/4),m=[Math.cos(p*Fo)-y*Math.sin(p*Fo),Math.sin(p*Fo)+y*Math.cos(p*Fo)],x=m[0],b=m[1],w=[Math.cos(d*Fo),Math.sin(d*Fo)],S=w[0],T=w[1],O=[S+y*Math.sin(d*Fo),T-y*Math.cos(d*Fo)],A=O[0],E=O[1];c[g]={relative:t.relative,type:Zo.CURVE_TO};var C=function(e,r){var n=Io([e*t.rX,r*t.rY],t.xRot),i=n[0],a=n[1];return[t.cX+i,t.cY+a]};n=C(x,b),c[g].x1=n[0],c[g].y1=n[1],i=C(A,E),c[g].x2=i[0],c[g].y2=i[1],a=C(S,T),c[g].x=a[0],c[g].y=a[1],t.relative&&(c[g].x1-=l,c[g].y1-=f,c[g].x2-=l,c[g].y2-=f,c[g].x-=l,c[g].y-=f),l=(s=[c[g].x,c[g].y])[0],f=s[1]}return c}(t,t.relative?0:e,t.relative?0:r):t})},t.ANNOTATE_ARCS=function(){return i(function(t,e,r){return t.relative&&(e=0,r=0),Zo.ARC===t.type&&Do(t,e,r),t})},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),a=n(),s=r(),o=i(function(e,r,n){var i=s(a(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function u(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function h(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(i.type&Zo.DRAWING_COMMANDS&&(u(r),h(n)),i.type&Zo.HORIZ_LINE_TO&&u(i.x),i.type&Zo.VERT_LINE_TO&&h(i.y),i.type&Zo.LINE_TO&&(u(i.x),h(i.y)),i.type&Zo.CURVE_TO){u(i.x),h(i.y);for(var c=0,l=Ho(r,i.x1,i.x2,i.x);c<l.length;c++)0<(C=l[c])&&1>C&&u(Xo(r,i.x1,i.x2,i.x,C));for(var f=0,g=Ho(n,i.y1,i.y2,i.y);f<g.length;f++)0<(C=g[f])&&1>C&&h(Xo(n,i.y1,i.y2,i.y,C))}if(i.type&Zo.ARC){u(i.x),h(i.y),Do(i,r,n);for(var p=i.xRot/180*Math.PI,d=Math.cos(p)*i.rX,v=Math.sin(p)*i.rX,y=-Math.sin(p)*i.rY,m=Math.cos(p)*i.rY,x=i.phi1<i.phi2?[i.phi1,i.phi2]:-180>i.phi2?[i.phi2+360,i.phi1+360]:[i.phi2,i.phi1],b=x[0],w=x[1],S=function(t){var e=t[0],r=t[1],n=180*Math.atan2(r,e)/Math.PI;return n<b?n+360:n},T=0,O=jo(y,-d,0).map(S);T<O.length;T++)(C=O[T])>b&&C<w&&u(Uo(i.cX,d,y,C));for(var A=0,E=jo(m,-v,0).map(S);A<E.length;A++){var C;(C=E[A])>b&&C<w&&h(Uo(i.cY,v,m,C))}}return e});return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(Bo||(Bo={}));var Yo,Wo,qo=function(){function t(){}return t.prototype.round=function(t){return this.transform(Bo.ROUND(t))},t.prototype.toAbs=function(){return this.transform(Bo.TO_ABS())},t.prototype.toRel=function(){return this.transform(Bo.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(Bo.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(Bo.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(Bo.QT_TO_C())},t.prototype.aToC=function(){return this.transform(Bo.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(Bo.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(Bo.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(Bo.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(Bo.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,n,i,a){return this.transform(Bo.MATRIX(t,e,r,n,i,a))},t.prototype.skewX=function(t){return this.transform(Bo.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(Bo.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(Bo.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(Bo.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(Bo.ANNOTATE_ARCS())},t}(),Go=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},Qo=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},$o=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return Vo(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var n=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},i=0;i<t.length;i++){var a=t[i],s=!(this.curCommandType!==Zo.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=Qo(a)&&("0"===this.curNumber&&"0"===a||s);if(!Qo(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||s){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError("Invalid number ending at "+i);if(this.curCommandType===Zo.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got "'+u+'" at index "'+i+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+i+'"');this.curArgs.push(u),this.curArgs.length===Ko[this.curCommandType]&&(Zo.HORIZ_LINE_TO===this.curCommandType?n({type:Zo.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):Zo.VERT_LINE_TO===this.curCommandType?n({type:Zo.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===Zo.MOVE_TO||this.curCommandType===Zo.LINE_TO||this.curCommandType===Zo.SMOOTH_QUAD_TO?(n({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),Zo.MOVE_TO===this.curCommandType&&(this.curCommandType=Zo.LINE_TO)):this.curCommandType===Zo.CURVE_TO?n({type:Zo.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===Zo.SMOOTH_CURVE_TO?n({type:Zo.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===Zo.QUAD_TO?n({type:Zo.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===Zo.ARC&&n({type:Zo.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!Go(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+i+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+i+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=Zo.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=Zo.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=Zo.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=Zo.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=Zo.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=Zo.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=Zo.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=Zo.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+i+".");this.curCommandType=Zo.ARC,this.curCommandRelative="a"===a}else e.push({type:Zo.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var n=0,i=Object.getPrototypeOf(this).parse.call(this,e);n<i.length;n++){var a=i[n],s=t(a);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(qo),Zo=function(t){function e(r){var n=t.call(this)||this;return n.commands="string"==typeof r?e.parse(r):r,n}return Vo(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=Bo.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,n=this.commands;r<n.length;r++){var i=t(n[r]);Array.isArray(i)?e.push.apply(e,i):e.push(i)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var n=t[r];if(n.type===Zo.CLOSE_PATH)e+="z";else if(n.type===Zo.HORIZ_LINE_TO)e+=(n.relative?"h":"H")+n.x;else if(n.type===Zo.VERT_LINE_TO)e+=(n.relative?"v":"V")+n.y;else if(n.type===Zo.MOVE_TO)e+=(n.relative?"m":"M")+n.x+" "+n.y;else if(n.type===Zo.LINE_TO)e+=(n.relative?"l":"L")+n.x+" "+n.y;else if(n.type===Zo.CURVE_TO)e+=(n.relative?"c":"C")+n.x1+" "+n.y1+" "+n.x2+" "+n.y2+" "+n.x+" "+n.y;else if(n.type===Zo.SMOOTH_CURVE_TO)e+=(n.relative?"s":"S")+n.x2+" "+n.y2+" "+n.x+" "+n.y;else if(n.type===Zo.QUAD_TO)e+=(n.relative?"q":"Q")+n.x1+" "+n.y1+" "+n.x+" "+n.y;else if(n.type===Zo.SMOOTH_QUAD_TO)e+=(n.relative?"t":"T")+n.x+" "+n.y;else{if(n.type!==Zo.ARC)throw new Error('Unexpected command type "'+n.type+'" at index '+r+".");e+=(n.relative?"a":"A")+n.rX+" "+n.rY+" "+n.xRot+" "+ +n.lArcFlag+" "+ +n.sweepFlag+" "+n.x+" "+n.y}}return e}(t)},e.parse=function(t){var e=new $o,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(qo),Ko=((Yo={})[Zo.MOVE_TO]=2,Yo[Zo.LINE_TO]=2,Yo[Zo.HORIZ_LINE_TO]=1,Yo[Zo.VERT_LINE_TO]=1,Yo[Zo.CLOSE_PATH]=0,Yo[Zo.QUAD_TO]=4,Yo[Zo.SMOOTH_QUAD_TO]=2,Yo[Zo.CURVE_TO]=6,Yo[Zo.SMOOTH_CURVE_TO]=4,Yo[Zo.ARC]=7,Yo),Jo={};function tu(t){return tu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tu(t)}!function(){if(Wo)return Jo;Wo=1;var t=Fe().PROPER,e=We(),r=we(),n=ma(),i=y(),a=Ka(),s="toString",o=RegExp.prototype,u=o[s],h=i(function(){return"/a/b"!==u.call({source:"a",flags:"b"})}),c=t&&u.name!==s;(h||c)&&e(o,s,function(){var t=r(this);return"/"+n(t.source)+"/"+n(a(t))},{unsafe:!0})}();
/**
			* StackBlur - a fast almost Gaussian Blur For Canvas
			*
			* In case you find this class useful - especially in commercial projects -
			* I am not totally unhappy for a small donation to my PayPal account
			* <EMAIL>
			*
			* Or support me on flattr:
			* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.
			*
			* @module StackBlur
			* <AUTHOR> Klingemann
			* Contact: <EMAIL>
			* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}
			* Twitter: @quasimondo
			*
			* @copyright (c) 2010 Mario Klingemann
			*
			* Permission is hereby granted, free of charge, to any person
			* obtaining a copy of this software and associated documentation
			* files (the "Software"), to deal in the Software without
			* restriction, including without limitation the rights to use,
			* copy, modify, merge, publish, distribute, sublicense, and/or sell
			* copies of the Software, and to permit persons to whom the
			* Software is furnished to do so, subject to the following
			* conditions:
			*
			* The above copyright notice and this permission notice shall be
			* included in all copies or substantial portions of the Software.
			*
			* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
			* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
			* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
			* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
			* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
			* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
			* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
			* OTHER DEALINGS IN THE SOFTWARE.
			*/
var eu=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],ru=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function nu(t,e,r,n,i,a){if(!(isNaN(a)||a<1)){a|=0;var s=function(t,e,r,n,i){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==tu(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var a=t.getContext("2d");try{return a.getImageData(e,r,n,i)}catch(s){throw new Error("unable to access image data: "+s)}}(t,e,r,n,i);s=function(t,e,r,n,i,a){for(var s,o=t.data,u=2*a+1,h=n-1,c=i-1,l=a+1,f=l*(l+1)/2,g=new iu,p=g,d=1;d<u;d++)p=p.next=new iu,d===l&&(s=p);p.next=g;for(var v=null,y=null,m=0,x=0,b=eu[a],w=ru[a],S=0;S<i;S++){p=g;for(var T=o[x],O=o[x+1],A=o[x+2],E=o[x+3],C=0;C<l;C++)p.r=T,p.g=O,p.b=A,p.a=E,p=p.next;for(var P=0,N=0,M=0,R=0,_=l*T,V=l*O,I=l*A,k=l*E,L=f*T,D=f*O,j=f*A,B=f*E,F=1;F<l;F++){var z=x+((h<F?h:F)<<2),U=o[z],H=o[z+1],X=o[z+2],Y=o[z+3],W=l-F;L+=(p.r=U)*W,D+=(p.g=H)*W,j+=(p.b=X)*W,B+=(p.a=Y)*W,P+=U,N+=H,M+=X,R+=Y,p=p.next}v=g,y=s;for(var q=0;q<n;q++){var G=B*b>>>w;if(o[x+3]=G,0!==G){var Q=255/G;o[x]=(L*b>>>w)*Q,o[x+1]=(D*b>>>w)*Q,o[x+2]=(j*b>>>w)*Q}else o[x]=o[x+1]=o[x+2]=0;L-=_,D-=V,j-=I,B-=k,_-=v.r,V-=v.g,I-=v.b,k-=v.a;var $=q+a+1;$=m+($<h?$:h)<<2,L+=P+=v.r=o[$],D+=N+=v.g=o[$+1],j+=M+=v.b=o[$+2],B+=R+=v.a=o[$+3],v=v.next;var Z=y,K=Z.r,J=Z.g,tt=Z.b,et=Z.a;_+=K,V+=J,I+=tt,k+=et,P-=K,N-=J,M-=tt,R-=et,y=y.next,x+=4}m+=n}for(var rt=0;rt<n;rt++){var nt=o[x=rt<<2],it=o[x+1],at=o[x+2],st=o[x+3],ot=l*nt,ut=l*it,ht=l*at,ct=l*st,lt=f*nt,ft=f*it,gt=f*at,pt=f*st;p=g;for(var dt=0;dt<l;dt++)p.r=nt,p.g=it,p.b=at,p.a=st,p=p.next;for(var vt=n,yt=0,mt=0,xt=0,bt=0,wt=1;wt<=a;wt++){x=vt+rt<<2;var St=l-wt;lt+=(p.r=nt=o[x])*St,ft+=(p.g=it=o[x+1])*St,gt+=(p.b=at=o[x+2])*St,pt+=(p.a=st=o[x+3])*St,bt+=nt,yt+=it,mt+=at,xt+=st,p=p.next,wt<c&&(vt+=n)}x=rt,v=g,y=s;for(var Tt=0;Tt<i;Tt++){var Ot=x<<2;o[Ot+3]=st=pt*b>>>w,st>0?(st=255/st,o[Ot]=(lt*b>>>w)*st,o[Ot+1]=(ft*b>>>w)*st,o[Ot+2]=(gt*b>>>w)*st):o[Ot]=o[Ot+1]=o[Ot+2]=0,lt-=ot,ft-=ut,gt-=ht,pt-=ct,ot-=v.r,ut-=v.g,ht-=v.b,ct-=v.a,Ot=rt+((Ot=Tt+l)<c?Ot:c)*n<<2,lt+=bt+=v.r=o[Ot],ft+=yt+=v.g=o[Ot+1],gt+=mt+=v.b=o[Ot+2],pt+=xt+=v.a=o[Ot+3],v=v.next,ot+=nt=y.r,ut+=it=y.g,ht+=at=y.b,ct+=st=y.a,bt-=nt,yt-=it,mt-=at,xt-=st,y=y.next,x+=n}}return t}(s,0,0,n,i,a),t.getContext("2d").putImageData(s,e,r)}}var iu=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};function au(t){return t.replace(/(?!\u3000)\s+/gm," ")}function su(t){return t.replace(/^[\n \t]+/,"")}function ou(t){return t.replace(/[\n \t]+$/,"")}function uu(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}t("presets",Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>ha(function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)})()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:n}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:n,createCanvas:r.createCanvas,createImage:r.loadImage}}}));var hu=/^[A-Z-]+$/;function cu(t){return hu.test(t)?t.toLowerCase():t}function lu(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function fu(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t)}var gu=/(\[[^\]]+\])/g,pu=/(#[^\s+>~.[:]+)/g,du=/(\.[^\s+>~.[:]+)/g,vu=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,yu=/(:[\w-]+\([^)]*\))/gi,mu=/(:[^\s+>~.[:]+)/g,xu=/([^\s+>~.[:]+)/g;function bu(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function wu(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),n=0;return[r,n]=bu(r,gu),e[1]+=n,[r,n]=bu(r,pu),e[0]+=n,[r,n]=bu(r,du),e[1]+=n,[r,n]=bu(r,vu),e[2]+=n,[r,n]=bu(r,yu),e[1]+=n,[r,n]=bu(r,mu),e[1]+=n,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,n]=bu(r,xu),e[2]+=n,e.join("")}var Su=t("PSEUDO_ZERO",1e-8);function Tu(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function Ou(t,e){return(t[0]*e[0]+t[1]*e[1])/(Tu(t)*Tu(e))}function Au(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Ou(t,e))}function Eu(t){return t*t*t}function Cu(t){return 3*t*t*(1-t)}function Pu(t){return 3*t*(1-t)*(1-t)}function Nu(t){return(1-t)*(1-t)*(1-t)}function Mu(t){return t*t}function Ru(t){return 2*t*(1-t)}function _u(t){return(1-t)*(1-t)}class Vu{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new Vu(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return au(this.getString()).trim().split(t).map(t=>new Vu(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=fu(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,n]="boolean"==typeof t?[void 0,t]:[t],{viewPort:i}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(i.computeSize("x"),i.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(i.computeSize("x"),i.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*i.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*i.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&n:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*i.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*i.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var n=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),n.hasValue()&&r.getAttribute("patternTransform",!0).setValue(n.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?Vu.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,n=0,i=0;i<r&&(","===e[i]&&n++,3!==n);i++);if(t.hasValue()&&this.isString()&&3!==n){var a=new So(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new Vu(this.document,this.name,e)}}t("Property",Vu),Vu.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class Iu{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}t("ViewPort",Iu);class ku{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,n=e]=uu(t);return new ku(r,n)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,n=r]=uu(t);return new ku(r,n)}static parsePath(t){for(var e=uu(t),r=e.length,n=[],i=0;i<r;i+=2)n.push(new ku(e[i],e[i+1]));return n}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,n=e*t[0]+r*t[2]+t[4],i=e*t[1]+r*t[3]+t[5];this.x=n,this.y=i}}t("Point",ku);class Lu{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,n=t.ctx.canvas;n.onclick=e,n.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:n}=t.ctx.canvas;n&&(n.cursor=""),e.forEach((t,e)=>{for(var{run:n}=t,i=r[e];i;)n(i),i=i.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:n}=this;r.forEach((r,i)=>{var{x:a,y:s}=r;!n[i]&&e.isPointInPath&&e.isPointInPath(a,s)&&(n[i]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:n}=this;r.forEach((r,i)=>{var{x:a,y:s}=r;!n[i]&&e.isPointInBox(a,s)&&(n[i]=t)})}}mapXY(t,e){for(var{window:r,ctx:n}=this.screen,i=new ku(t,e),a=n.canvas;a;)i.x-=a.offsetLeft,i.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(i.x+=r.scrollX),r.scrollY&&(i.y+=r.scrollY),i}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}t("Mouse",Lu);var Du="undefined"!=typeof window?window:null,ju="undefined"!=typeof fetch?fetch.bind(void 0):null;class Bu{constructor(t){var{fetch:e=ju,window:r=Du}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new Iu,this.mouse=new Lu(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:n,width:i,desiredWidth:a,height:s,desiredHeight:o,minX:u=0,minY:h=0,refX:c,refY:l,clip:f=!1,clipX:g=0,clipY:p=0}=t,d=au(n).replace(/^defer\s/,""),[v,y]=d.split(" "),m=v||"xMidYMid",x=y||"meet",b=i/a,w=s/o,S=Math.min(b,w),T=Math.max(b,w),O=a,A=o;"meet"===x&&(O*=S,A*=S),"slice"===x&&(O*=T,A*=T);var E=new Vu(e,"refX",c),C=new Vu(e,"refY",l),P=E.hasValue()&&C.hasValue();if(P&&r.translate(-S*E.getPixels("x"),-S*C.getPixels("y")),f){var N=S*g,M=S*p;r.beginPath(),r.moveTo(N,M),r.lineTo(i,M),r.lineTo(i,s),r.lineTo(N,s),r.closePath(),r.clip()}if(!P){var R="meet"===x&&S===w,_="slice"===x&&T===w,V="meet"===x&&S===b,I="slice"===x&&T===b;m.startsWith("xMid")&&(R||_)&&r.translate(i/2-O/2,0),m.endsWith("YMid")&&(V||I)&&r.translate(0,s/2-A/2),m.startsWith("xMax")&&(R||_)&&r.translate(i-O,0),m.endsWith("YMax")&&(V||I)&&r.translate(0,s-A)}switch(!0){case"none"===m:r.scale(b,w);break;case"meet"===x:r.scale(S,S);break;case"slice"===x:r.scale(T,T)}r.translate(-u,-h)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:n=!1,ignoreDimensions:i=!1,ignoreClear:a=!1,forceRedraw:s,scaleWidth:o,scaleHeight:u,offsetX:h,offsetY:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:l,mouse:f}=this,g=1e3/l;if(this.frameDuration=g,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,i,a,o,u,h,c),e){var p=Date.now(),d=p,v=0,y=()=>{p=Date.now(),(v=p-d)>=g&&(d=p-v%g,this.shouldUpdate(n,s)&&(this.render(t,i,a,o,u,h,c),f.runEvents())),this.intervalId=uo(y)};r||f.start(),this.intervalId=uo(y)}}stop(){this.intervalId&&(uo.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce((t,e)=>e.update(r)||t,!1))return!0}return!("function"!=typeof e||!e())||!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents()}render(t,e,r,n,i,a,s){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:h,ctx:c,isFirstRender:l}=this,f=c.canvas;h.clear(),f.width&&f.height?h.setCurrent(f.width,f.height):h.setCurrent(o,u);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(l||"number"!=typeof n&&"number"!=typeof i)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),v=p.getPixels("y")),h.setCurrent(d,v),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof s&&t.getAttribute("y",!0).setValue(s),"number"==typeof n||"number"==typeof i){var y=uu(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof n){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/n:isNaN(y[2])||(m=y[2]/n)}if("number"==typeof i){var w=t.getStyle("height");w.hasValue()?x=w.getPixels("y")/i:isNaN(y[3])||(x=y[3]/i)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(n),t.getAttribute("height",!0).setValue(i);var S=t.getStyle("transform",!0,!0);S.setValue("".concat(S.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||c.clearRect(0,0,d,v),t.render(c),l&&(this.isFirstRender=!1)}}t("Screen",Bu),Bu.defaultWindow=Du,Bu.defaultFetch=ju;var{defaultFetch:Fu}=Bu,zu="undefined"!=typeof DOMParser?DOMParser:null;class Uu{constructor(){var{fetch:t=Fu,DOMParser:e=zu}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return ha(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return ha(function*(){var r=yield e.fetch(t),n=yield r.text();return e.parseFromString(n)})()}}t("Parser",Uu);class Hu{constructor(t,e){this.type="translate",this.point=null,this.point=ku.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}t("Translate",Hu);class Xu{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var n=uu(e);this.angle=new Vu(t,"angle",n[0]),this.originX=r[0],this.originY=r[1],this.cx=n[1]||0,this.cy=n[2]||0}apply(t){var{cx:e,cy:r,originX:n,originY:i,angle:a}=this,s=e+n.getPixels("x"),o=r+i.getPixels("y");t.translate(s,o),t.rotate(a.getRadians()),t.translate(-s,-o)}unapply(t){var{cx:e,cy:r,originX:n,originY:i,angle:a}=this,s=e+n.getPixels("x"),o=r+i.getPixels("y");t.translate(s,o),t.rotate(-1*a.getRadians()),t.translate(-s,-o)}applyToPoint(t){var{cx:e,cy:r,angle:n}=this,i=n.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(i),Math.sin(i),-Math.sin(i),Math.cos(i),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}t("Rotate",Xu);class Yu{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var n=ku.parseScale(e);0!==n.x&&0!==n.y||(n.x=Su,n.y=Su),this.scale=n,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:n,originY:i}=this,a=n.getPixels("x"),s=i.getPixels("y");t.translate(a,s),t.scale(e,r||e),t.translate(-a,-s)}unapply(t){var{scale:{x:e,y:r},originX:n,originY:i}=this,a=n.getPixels("x"),s=i.getPixels("y");t.translate(a,s),t.scale(1/e,1/r||e),t.translate(-a,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}t("Scale",Yu);class Wu{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=uu(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:n}=this,i=e.getPixels("x"),a=r.getPixels("y");t.translate(i,a),t.transform(n[0],n[1],n[2],n[3],n[4],n[5]),t.translate(-i,-a)}unapply(t){var{originX:e,originY:r,matrix:n}=this,i=n[0],a=n[2],s=n[4],o=n[1],u=n[3],h=n[5],c=1/(i*(1*u-0*h)-a*(1*o-0*h)+s*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(c*(1*u-0*h),c*(0*h-1*o),c*(0*s-1*a),c*(1*i-0*s),c*(a*h-s*u),c*(s*o-i*h)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}t("Matrix",Wu);class qu extends Wu{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new Vu(t,"angle",e)}}t("Skew",qu);class Gu extends qu{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}t("SkewX",Gu);class Qu extends qu{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}t("SkewY",Qu);class $u{constructor(t,e,r){this.document=t,this.transforms=[];var n=function(t){return au(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e);n.forEach(t=>{if("none"!==t){var[e,n]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),i=$u.transformTypes[e];void 0!==i&&this.transforms.push(new i(this.document,n,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[n,i=n]=e.getStyle("transform-origin",!1,!0).split(),a=[n,i];return r.hasValue()?new $u(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,n=0;n<r;n++)e[n].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,n=0;n<r;n++)e[n].applyToPoint(t)}}t("Transform",$u),$u.transformTypes={translate:Hu,rotate:Xu,scale:Yu,matrix:Wu,skewX:Gu,skewY:Qu};class Zu{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach(e=>{var r=cu(e.nodeName);this.attributes[r]=new Vu(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var n=this.getAttribute("style").getString().split(";").map(t=>t.trim());n.forEach(e=>{if(e){var[r,n]=e.split(":").map(t=>t.trim());this.styles[r]=new Vu(t,r,n)}})}var{definitions:i}=t,a=this.getAttribute("id");a.hasValue()&&(i[a.getString()]||(i[a.getString()]=this)),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var n=t.createTextNode(e);n.getText().length>0&&this.addChild(n)}})}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var n=new Vu(this.document,t,"");return this.attributes[t]=n,n}return r||Vu.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return Vu.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.styles[t];if(n)return n;var i=this.getAttribute(t);if(null!=i&&i.hasValue())return this.styles[t]=i,i;if(!r){var{parent:a}=this;if(a){var s=a.getStyle(t);if(null!=s&&s.hasValue())return s}}if(e){var o=new Vu(this.document,t,"");return this.styles[t]=o,o}return n||Vu.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=$u.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var n=r.getDefinition();n&&n.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof Zu?t:this.document.createElement(t);e.parent=this,Zu.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var n=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!n||""===n)&&n.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var n=t[r],i=e[r];if(n)for(var a in n){var s=this.stylesSpecificity[a];void 0===s&&(s="000"),i>=s&&(this.styles[a]=n[a],this.stylesSpecificity[a]=i)}}}removeStyles(t,e){return e.reduce((e,r)=>{var n=t.getStyle(r);if(!n.hasValue())return e;var i=n.getString();return n.setValue(""),[...e,[r,i]]},[])}restoreStyles(t,e){e.forEach(e=>{var[r,n]=e;t.getStyle(r,!0).setValue(n)})}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}t("Element",Zu),Zu.ignoreChildTypes=["title"];class Ku extends Zu{constructor(t,e,r){super(t,e,r)}}function Ju(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function th(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function eh(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}t("UnknownElement",Ku);class rh{constructor(t,e,r,n,i,a){var s=a?"string"==typeof a?rh.parse(a):a:{};this.fontFamily=i||s.fontFamily,this.fontSize=n||s.fontSize,this.fontStyle=t||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=e||s.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",n="",i="",a="",s=au(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return s.forEach(t=>{switch(!0){case!o.fontStyle&&rh.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&rh.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&rh.weights.includes(t):"inherit"!==t&&(n=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([i]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}}),new rh(e,r,n,i,a,t)}toString(){return[th(this.fontStyle),this.fontVariant,eh(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"==typeof process?t:t.trim().split(",").map(Ju).join(","))].join(" ").trim();var t}}t("Font",rh),rh.styles="normal|italic|oblique|inherit",rh.variants="normal|small-caps|inherit",rh.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class nh{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=n,this.addPoint(t,e),this.addPoint(r,n)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:n,y2:i}=t;this.addPoint(e,r),this.addPoint(n,i)}}sumCubic(t,e,r,n,i){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*n+Math.pow(t,3)*i}bezierCurveAdd(t,e,r,n,i){var a=6*e-12*r+6*n,s=-3*e+9*r-9*n+3*i,o=3*r-3*e;if(0!==s){var u=Math.pow(a,2)-4*o*s;if(!(u<0)){var h=(-a+Math.sqrt(u))/(2*s);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,n,i)):this.addY(this.sumCubic(h,e,r,n,i)));var c=(-a-Math.sqrt(u))/(2*s);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,n,i)):this.addY(this.sumCubic(c,e,r,n,i)))}}else{if(0===a)return;var l=-o/a;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,n,i)):this.addY(this.sumCubic(l,e,r,n,i)))}}addBezierCurve(t,e,r,n,i,a,s,o){this.addPoint(t,e),this.addPoint(s,o),this.bezierCurveAdd(!0,t,r,i,s),this.bezierCurveAdd(!1,e,n,a,o)}addQuadraticCurve(t,e,r,n,i,a){var s=t+2/3*(r-t),o=e+2/3*(n-e),u=s+1/3*(i-t),h=o+1/3*(a-e);this.addBezierCurve(t,e,s,u,o,h,i,a)}isPointInBox(t,e){var{x1:r,y1:n,x2:i,y2:a}=this;return r<=t&&t<=i&&n<=e&&e<=a}}t("BoundingBox",nh);class ih extends Zo{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new ku(0,0),this.control=new ku(0,0),this.current=new ku(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new ku(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==Zo.CURVE_TO&&t!==Zo.SMOOTH_CURVE_TO&&t!==Zo.QUAD_TO&&t!==Zo.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:n,y:i}}=this;return new ku(2*e-n,2*r-i)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:n,angles:i}=this;r&&i.length>0&&!i[i.length-1]&&(i[i.length-1]=n[n.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var n=r+1;n<e;n++)if(t[n]){t[r]=t[n];break}return t}}t("PathParser",ih);class ah extends Zu{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),n=this.getStyle("fill-opacity"),i=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,n);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(n.hasValue()){var u=new Vu(this.document,"fill",t.fillStyle).addOpacity(n).getColor();t.fillStyle=u}if(i.isUrlDefinition()){var h=i.getFillStyleDefinition(this,a);h&&(t.strokeStyle=h)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var c=i.getString();"inherit"!==c&&(t.strokeStyle="none"===c?"rgba(0,0,0,0)":c)}if(a.hasValue()){var l=new Vu(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var g=f.getPixels();t.lineWidth=g||Su}var p=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),y=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),d.hasValue()&&(t.lineJoin=d.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),y.hasValue()&&"none"!==y.getString()){var x=uu(y.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),S=this.getStyle("font-style"),T=this.getStyle("font-variant"),O=this.getStyle("font-weight"),A=this.getStyle("font-size"),E=this.getStyle("font-family"),C=new rh(S.getString(),T.getString(),O.getString(),A.hasValue()?"".concat(A.getPixels(!0),"px"):"",E.getString(),rh.parse(w.getString(),t.font));S.setValue(C.fontStyle),T.setValue(C.fontVariant),O.setValue(C.fontWeight),A.setValue(C.fontSize),E.setValue(C.fontFamily),t.font=C.toString(),A.isPixels()&&(this.document.emSize=A.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}t("RenderedElement",ah);class sh extends ah{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new ih(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new nh;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case ih.MOVE_TO:this.pathM(t,r);break;case ih.LINE_TO:this.pathL(t,r);break;case ih.HORIZ_LINE_TO:this.pathH(t,r);break;case ih.VERT_LINE_TO:this.pathV(t,r);break;case ih.CURVE_TO:this.pathC(t,r);break;case ih.SMOOTH_CURVE_TO:this.pathS(t,r);break;case ih.QUAD_TO:this.pathQ(t,r);break;case ih.SMOOTH_QUAD_TO:this.pathT(t,r);break;case ih.ARC:this.pathA(t,r);break;case ih.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles(),n=e.map((t,e)=>[t,r[e]]);return n}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var n=r.length-1,i=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(i.isUrlDefinition()){var o=i.getDefinition(),[u,h]=r[0];o.render(t,u,h)}if(a.isUrlDefinition())for(var c=a.getDefinition(),l=1;l<n;l++){var[f,g]=r[l];c.render(t,f,g)}if(s.isUrlDefinition()){var p=s.getDefinition(),[d,v]=r[n];p.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:n}=sh.pathM(r),{x:i,y:a}=n;r.addMarker(n),e.addPoint(i,a),t&&t.moveTo(i,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:n,point:i}=sh.pathL(r),{x:a,y:s}=i;r.addMarker(i,n),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathH(t){var{current:e,command:r}=t,n=new ku((r.relative?e.x:0)+r.x,e.y);return t.current=n,{current:e,point:n}}pathH(t,e){var{pathParser:r}=this,{current:n,point:i}=sh.pathH(r),{x:a,y:s}=i;r.addMarker(i,n),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathV(t){var{current:e,command:r}=t,n=new ku(e.x,(r.relative?e.y:0)+r.y);return t.current=n,{current:e,point:n}}pathV(t,e){var{pathParser:r}=this,{current:n,point:i}=sh.pathV(r),{x:a,y:s}=i;r.addMarker(i,n),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:n,point:i,controlPoint:a,currentPoint:s}=sh.pathC(r);r.addMarker(s,a,i),e.addBezierCurve(n.x,n.y,i.x,i.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,a.x,a.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:n,point:i,controlPoint:a,currentPoint:s}=sh.pathS(r);r.addMarker(s,a,i),e.addBezierCurve(n.x,n.y,i.x,i.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,a.x,a.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:n,controlPoint:i,currentPoint:a}=sh.pathQ(r);r.addMarker(a,i,i),e.addQuadraticCurve(n.x,n.y,i.x,i.y,a.x,a.y),t&&t.quadraticCurveTo(i.x,i.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:n,controlPoint:i,currentPoint:a}=sh.pathT(r);r.addMarker(a,i,i),e.addQuadraticCurve(n.x,n.y,i.x,i.y,a.x,a.y),t&&t.quadraticCurveTo(i.x,i.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:n,rY:i,xRot:a,lArcFlag:s,sweepFlag:o}=r,u=a*(Math.PI/180),h=t.getAsCurrentPoint(),c=new ku(Math.cos(u)*(e.x-h.x)/2+Math.sin(u)*(e.y-h.y)/2,-Math.sin(u)*(e.x-h.x)/2+Math.cos(u)*(e.y-h.y)/2),l=Math.pow(c.x,2)/Math.pow(n,2)+Math.pow(c.y,2)/Math.pow(i,2);l>1&&(n*=Math.sqrt(l),i*=Math.sqrt(l));var f=(s===o?-1:1)*Math.sqrt((Math.pow(n,2)*Math.pow(i,2)-Math.pow(n,2)*Math.pow(c.y,2)-Math.pow(i,2)*Math.pow(c.x,2))/(Math.pow(n,2)*Math.pow(c.y,2)+Math.pow(i,2)*Math.pow(c.x,2)));isNaN(f)&&(f=0);var g=new ku(f*n*c.y/i,f*-i*c.x/n),p=new ku((e.x+h.x)/2+Math.cos(u)*g.x-Math.sin(u)*g.y,(e.y+h.y)/2+Math.sin(u)*g.x+Math.cos(u)*g.y),d=Au([1,0],[(c.x-g.x)/n,(c.y-g.y)/i]),v=[(c.x-g.x)/n,(c.y-g.y)/i],y=[(-c.x-g.x)/n,(-c.y-g.y)/i],m=Au(v,y);return Ou(v,y)<=-1&&(m=Math.PI),Ou(v,y)>=1&&(m=0),{currentPoint:h,rX:n,rY:i,sweepFlag:o,xAxisRotation:u,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:n,rX:i,rY:a,sweepFlag:s,xAxisRotation:o,centp:u,a1:h,ad:c}=sh.pathA(r),l=1-s?1:-1,f=h+l*(c/2),g=new ku(u.x+i*Math.cos(f),u.y+a*Math.sin(f));if(r.addMarkerAngle(g,f-l*Math.PI/2),r.addMarkerAngle(n,f-l*Math.PI),e.addPoint(n.x,n.y),t&&!isNaN(h)&&!isNaN(c)){var p=i>a?i:a,d=i>a?1:i/a,v=i>a?a/i:1;t.translate(u.x,u.y),t.rotate(o),t.scale(d,v),t.arc(0,0,p,h,h+c,Boolean(1-s)),t.scale(1/d,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){sh.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}t("PathElement",sh);class oh extends sh{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}t("GlyphElement",oh);class uh extends ah{constructor(t,e,r){super(t,e,new.target===uh||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,n)=>{var i=this.getChildBoundingBox(t,this,this,n);e?e.addBoundingBox(i):e=i}),e}getFontSize(){var{document:t,parent:e}=this,r=rh.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new nh(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var n=e[r],i=null;if(t.isArabic){var a=e.length,s=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===s)&&r<a-1&&" "!==o&&(u="terminal"),r>0&&" "!==s&&r<a-1&&" "!==o&&(u="medial"),r>0&&" "!==s&&(r===a-1||" "===o)&&(u="initial"),void 0!==t.glyphs[n]){var h=t.glyphs[n];i=h instanceof oh?h:h[u]}}else i=t.glyphs[n];return i||(i=t.missingGlyph),i}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),n=r.indexOf(e),i=r.length-1,a=au(e.textContent||"");return 0===n&&(a=su(a)),n===i&&(a=ou(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,n=this.getText(),i=r.getStyle("font-family").getDefinition();if(i)for(var{unitsPerEm:a}=i.fontFace,s=rh.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(s.fontSize),u=r.getStyle("font-style").getString(s.fontStyle),h=o/a,c=i.isRTL?n.split("").reverse().join(""):n,l=uu(r.getAttribute("dx").getString()),f=c.length,g=0;g<f;g++){var p=this.getGlyph(i,c,g);t.translate(this.x,this.y),t.scale(h,-h);var d=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===u&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||i.horizAdvX)/a,void 0===l[g]||isNaN(l[g])||(this.x+=l[g])}else{var{x:v,y:y}=this;t.fillStyle&&t.fillText(n,v,y),t.strokeStyle&&t.strokeText(n,v,y)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,n){var i=r.children[n];i.children.length>0?i.children.forEach((r,n)=>{e.adjustChildCoordinatesRecursiveCore(t,e,i,n)}):this.adjustChildCoordinates(t,e,r,n)}adjustChildCoordinates(t,e,r,n){var i=r.children[n];if("function"!=typeof i.measureText)return i;t.save(),i.setContext(t,!0);var a=i.getAttribute("x"),s=i.getAttribute("y"),o=i.getAttribute("dx"),u=i.getAttribute("dy"),h=i.getStyle("font-family").getDefinition(),c=Boolean(h)&&h.isRTL;0===n&&(a.hasValue()||a.setValue(i.getInheritedAttribute("x")),s.hasValue()||s.setValue(i.getInheritedAttribute("y")),o.hasValue()||o.setValue(i.getInheritedAttribute("dx")),u.hasValue()||u.setValue(i.getInheritedAttribute("dy")));var l=i.measureText(t);return c&&(e.x-=l),a.hasValue()?(e.applyAnchoring(),i.x=a.getPixels("x"),o.hasValue()&&(i.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),i.x=e.x),e.x=i.x,c||(e.x+=l),s.hasValue()?(i.y=s.getPixels("y"),u.hasValue()&&(i.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),i.y=e.y),e.y=i.y,e.leafTexts.push(i),e.minX=Math.min(e.minX,i.x,i.x+l),e.maxX=Math.max(e.maxX,i.x,i.x+l),i.clearContext(t),t.restore(),i}getChildBoundingBox(t,e,r,n){var i=r.children[n];if("function"!=typeof i.getBoundingBox)return null;var a=i.getBoundingBox(t);return a?(i.children.forEach((r,n)=>{var s=e.getChildBoundingBox(t,e,i,n);a.addBoundingBox(s)}),a):null}renderChild(t,e,r,n){var i=r.children[n];i.render(t),i.children.forEach((r,n)=>{e.renderChild(t,e,i,n)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),n=this.measureTargetText(t,r);return this.measureCache=n,n}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,n=r.getStyle("font-family").getDefinition();if(n){for(var i=this.getFontSize(),a=n.isRTL?e.split("").reverse().join(""):e,s=uu(r.getAttribute("dx").getString()),o=a.length,u=0,h=0;h<o;h++)u+=(this.getGlyph(n,a,h).horizAdvX||n.horizAdvX)*i/n.fontFace.unitsPerEm,void 0===s[h]||isNaN(s[h])||(u+=s[h]);return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:c}=t.measureText(e);return this.clearContext(t),t.restore(),c}getInheritedAttribute(t){for(var e=this;e instanceof uh&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}t("TextElement",uh);class hh extends uh{constructor(t,e,r){super(t,e,new.target===hh||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}t("TSpanElement",hh);class ch extends hh{constructor(){super(...arguments),this.type="textNode"}}class lh extends ah{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:n,window:i}=r,a=t.canvas;if(n.setDefaults(t),a.style&&void 0!==t.font&&i&&void 0!==i.getComputedStyle){t.font=i.getComputedStyle(a).getPropertyValue("font");var s=new Vu(r,"fontSize",rh.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=n.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),c=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?uu(l.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,v=0,y=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=p,y=d,p=0,d=0)),n.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:n.viewPort.width,desiredWidth:o,height:n.viewPort.height,desiredHeight:u,minX:p,minY:d,refX:h.getValue(),refY:c.getValue(),clip:g,clipX:v,clipY:y}),f&&(n.viewPort.removeCurrent(),n.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.getAttribute("width",!0),i=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),s=this.getAttribute("style"),o=n.getNumber(0),u=i.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(n.setValue(t),i.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(u||e)),s.hasValue()){var c=this.getStyle("width"),l=this.getStyle("height");c.hasValue()&&c.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}t("SVGElement",lh);class fh extends sh{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),n=this.getStyle("width",!1,!0).getPixels("x"),i=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),s=this.getAttribute("ry"),o=a.getPixels("x"),u=s.getPixels("y");if(a.hasValue()&&!s.hasValue()&&(u=o),s.hasValue()&&!a.hasValue()&&(o=u),o=Math.min(o,n/2),u=Math.min(u,i/2),t){var h=(Math.sqrt(2)-1)/3*4;t.beginPath(),i>0&&n>0&&(t.moveTo(e+o,r),t.lineTo(e+n-o,r),t.bezierCurveTo(e+n-o+h*o,r,e+n,r+u-h*u,e+n,r+u),t.lineTo(e+n,r+i-u),t.bezierCurveTo(e+n,r+i-u+h*u,e+n-o+h*o,r+i,e+n-o,r+i),t.lineTo(e+o,r+i),t.bezierCurveTo(e+o-h*o,r+i,e,r+i-u+h*u,e,r+i-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-h*u,e+o-h*o,r,e+o,r),t.closePath())}return new nh(e,r,e+n,r+i)}getMarkers(){return null}}t("RectElement",fh);class gh extends sh{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),n=this.getAttribute("r").getPixels();return t&&n>0&&(t.beginPath(),t.arc(e,r,n,0,2*Math.PI,!1),t.closePath()),new nh(e-n,r-n,e+n,r+n)}getMarkers(){return null}}t("CircleElement",gh);class ph extends sh{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),n=this.getAttribute("ry").getPixels("y"),i=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&n>0&&(t.beginPath(),t.moveTo(i+r,a),t.bezierCurveTo(i+r,a+e*n,i+e*r,a+n,i,a+n),t.bezierCurveTo(i-e*r,a+n,i-r,a+e*n,i-r,a),t.bezierCurveTo(i-r,a-e*n,i-e*r,a-n,i,a-n),t.bezierCurveTo(i+e*r,a-n,i+r,a-e*n,i+r,a),t.closePath()),new nh(i-r,a-n,i+r,a+n)}getMarkers(){return null}}t("EllipseElement",ph);class dh extends sh{constructor(){super(...arguments),this.type="line"}getPoints(){return[new ku(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new ku(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:n,y:i}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(n,i)),new nh(e,r,n,i)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}t("LineElement",dh);class vh extends sh{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=ku.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:n}]=e,i=new nh(r,n);return t&&(t.beginPath(),t.moveTo(r,n)),e.forEach(e=>{var{x:r,y:n}=e;i.addPoint(r,n),t&&t.lineTo(r,n)}),i}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((n,i)=>{i!==e&&r.push([n,n.angleTo(t[i+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}t("PolylineElement",vh);class yh extends vh{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:n}]=this.points;return t&&(t.lineTo(r,n),t.closePath()),e}}t("PolygonElement",yh);class mh extends Zu{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var n=this.getStyle("width").getPixels("x",!0),i=this.getStyle("height").getPixels("y",!0),a=new lh(this.document,null);a.attributes.viewBox=new Vu(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new Vu(this.document,"width","".concat(n,"px")),a.attributes.height=new Vu(this.document,"height","".concat(i,"px")),a.attributes.transform=new Vu(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var s=this.document.createCanvas(n,i),o=s.getContext("2d"),u=this.getAttribute("x"),h=this.getAttribute("y");u.hasValue()&&h.hasValue()&&o.translate(u.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var c=-1;c<=1;c++)for(var l=-1;l<=1;l++)o.save(),a.attributes.x=new Vu(this.document,"x",c*s.width),a.attributes.y=new Vu(this.document,"y",l*s.height),a.render(o),o.restore();return t.createPattern(s,"repeat")}}t("PatternElement",mh);class xh extends Zu{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:n,y:i}=e,a=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(n,i),"auto"===a&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new lh(this.document,null);o.type=this.type,o.attributes.viewBox=new Vu(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new Vu(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new Vu(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new Vu(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new Vu(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new Vu(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new Vu(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new Vu(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-n,-i)}}}t("MarkerElement",xh);class bh extends Zu{constructor(){super(...arguments),this.type="defs"}render(){}}t("DefsElement",bh);class wh extends ah{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new nh;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}t("GElement",wh);class Sh extends Zu{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:n,children:i}=this;i.forEach(t=>{"stop"===t.type&&n.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var n=this;this.getHrefAttribute().hasValue()&&(n=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(n));var{stops:i}=n,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,i[i.length-1].color);if(i.forEach(t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=s.screen,[h]=u.viewPorts,c=new fh(s,null);c.attributes.x=new Vu(s,"x",-o/3),c.attributes.y=new Vu(s,"y",-o/3),c.attributes.width=new Vu(s,"width",o),c.attributes.height=new Vu(s,"height",o);var l=new wh(s,null);l.attributes.transform=new Vu(s,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[c];var f=new lh(s,null);f.attributes.x=new Vu(s,"x",0),f.attributes.y=new Vu(s,"y",0),f.attributes.width=new Vu(s,"width",h.width),f.attributes.height=new Vu(s,"height",h.height),f.children=[l];var g=s.createCanvas(h.width,h.height),p=g.getContext("2d");return p.fillStyle=a,f.render(p),p.createPattern(g,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new Vu(this.document,"color",e).addOpacity(t).getColor():e}}t("GradientElement",Sh);class Th extends Sh{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),n=r?e.getBoundingBox(t):null;if(r&&!n)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var i=r?n.x+n.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?n.y+n.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?n.x+n.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?n.y+n.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return i===s&&a===o?null:t.createLinearGradient(i,a,s,o)}}t("LinearGradientElement",Th);class Oh extends Sh{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),n=e.getBoundingBox(t);if(r&&!n)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var i=r?n.x+n.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?n.y+n.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=i,o=a;this.getAttribute("fx").hasValue()&&(s=r?n.x+n.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?n.y+n.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(n.width+n.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,o,h,i,a,u)}}t("RadialGradientElement",Oh);class Ah extends Zu{constructor(t,e,r){super(t,e,r),this.type="stop";var n=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),i=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),i.hasValue()&&(a=a.addOpacity(i)),this.offset=n,this.color=a.getColor()}}t("StopElement",Ah);class Eh extends Zu{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new Vu(t,"values",null);var n=this.getAttribute("values");n.hasValue()&&this.values.setValue(n.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:n}=this.getProgress(),i=r.getNumber()+(n.getNumber()-r.getNumber())*e;return"%"===t&&(i*=100),"".concat(i).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var n=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==n||this.frozen){if("remove"===n&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var i=!1;if(this.begin<this.duration){var a=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var o=s.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),i=!0}return i}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var n=r.progress*(e.getValue().length-1),i=Math.floor(n),a=Math.ceil(n);r.from=new Vu(t,"from",parseFloat(e.getValue()[i])),r.to=new Vu(t,"to",parseFloat(e.getValue()[a])),r.progress=(n-i)/(a-i)}else r.from=this.from,r.to=this.to;return r}}t("AnimateElement",Eh);class Ch extends Eh{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),n=new So(e.getColor()),i=new So(r.getColor());if(n.ok&&i.ok){var a=n.r+(i.r-n.r)*t,s=n.g+(i.g-n.g)*t,o=n.b+(i.b-n.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(s),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}t("AnimateColorElement",Ch);class Ph extends Eh{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),n=uu(e.getString()),i=uu(r.getString()),a=n.map((e,r)=>e+(i[r]-e)*t).join(" ");return a}}t("AnimateTransformElement",Ph);class Nh extends Zu{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:n}=t,{children:i}=this;for(var a of i)switch(a.type){case"font-face":this.fontFace=a;var s=a.getStyle("font-family");s.hasValue()&&(n[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]=Object.create(null)),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}t("FontElement",Nh);class Mh extends Zu{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}t("FontFaceElement",Mh);class Rh extends sh{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}t("MissingGlyphElement",Rh);class _h extends uh{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}t("TRefElement",_h);class Vh extends uh{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:n}=e,i=n[0],a=n.length>0&&Array.from(n).every(t=>3===t.nodeType);this.hasText=a,this.text=a?this.getTextFromNode(i):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:n}=this,{mouse:i}=e.screen,a=new Vu(e,"fontSize",rh.parse(e.ctx.font).fontSize);i.isWorking()&&i.checkBoundingBox(this,new nh(r,n-a.getPixels("y"),r+this.measureText(t),n))}else if(this.children.length>0){var s=new wh(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function Ih(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function kh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ih(Object(r),!0).forEach(function(e){Xs(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ih(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}t("AElement",Vh);class Lh extends uh{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var n=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(n)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:n}=e;switch(r){case ih.LINE_TO:t&&t.lineTo(n[0],n[1]);break;case ih.MOVE_TO:t&&t.moveTo(n[0],n[1]);break;case ih.CURVE_TO:t&&t.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case ih.QUAD_TO:t&&t.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case ih.ARC:var[i,a,s,o,u,h,c,l]=n,f=s>o?s:o,g=s>o?1:s/o,p=s>o?o/s:1;t&&(t.translate(i,a),t.rotate(c),t.scale(g,p),t.arc(0,0,f,u,u+h,Boolean(1-l)),t.scale(1/g,1/p),t.rotate(-c),t.translate(-i,-a));break;case ih.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:n}=this,i=t.fillStyle;"underline"===e&&t.beginPath(),n.forEach((n,i)=>{var{p0:a,p1:s,rotation:o,text:u}=n;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===i&&t.moveTo(a.x,a.y+r/8),t.lineTo(s.x,s.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=i,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,n,i,a,s,o,u){var h=a,c=this.measureText(t,o);" "===o&&"justify"===e&&r<n&&(c+=(n-r)/i),u>-1&&(h+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(h,l,0),g=this.getEquidistantPointOnPath(h+c,l,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(s){var v=Math.cos(Math.PI/2+d)*s,y=Math.cos(-d)*s;p.p0=kh(kh({},f),{},{x:f.x+v,y:f.y+y}),p.p1=kh(kh({},g),{},{x:g.x+v,y:g.y+y})}return{offset:h+=c,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,n=e||this.getText();if(r.has(n))return r.get(n);var i=this.measureTargetText(t,n);return r.set(n,i),i}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),n=e.split(" ").length-1,i=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),a=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),h=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(h=o.getPixels()):h=u.getPixels();var c=[],l=e.length;this.letterSpacingCache=c;for(var f=0;f<l;f++)c.push(void 0!==i[f]?i[f]:h);var g=c.reduce((t,e,r)=>0===r?0:t+e||0,0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==s&&"center"!==s||(m=-d/2),"end"!==s&&"right"!==s||(m=-d),m+=y,r.forEach((e,i)=>{var{offset:o,segment:u,rotation:h}=this.findSegmentToFitChar(t,s,d,v,n,m,a,e,i);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[i],p0:u.p0,p1:u.p1,rotation:h})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:n}=r,i=n?n.x:0,a=n?n.y:0,s=r.next(),o=s.type,u=[];switch(s.type){case ih.MOVE_TO:this.pathM(r,u);break;case ih.LINE_TO:o=this.pathL(r,u);break;case ih.HORIZ_LINE_TO:o=this.pathH(r,u);break;case ih.VERT_LINE_TO:o=this.pathV(r,u);break;case ih.CURVE_TO:this.pathC(r,u);break;case ih.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case ih.QUAD_TO:this.pathQ(r,u);break;case ih.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case ih.ARC:u=this.pathA(r);break;case ih.CLOSE_PATH:sh.pathZ(r)}s.type!==ih.CLOSE_PATH?e.push({type:o,points:u,start:{x:i,y:a},pathLength:this.calcLength(i,a,o,u)}):e.push({type:ih.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:n}=sh.pathM(t).point;e.push(r,n)}pathL(t,e){var{x:r,y:n}=sh.pathL(t).point;return e.push(r,n),ih.LINE_TO}pathH(t,e){var{x:r,y:n}=sh.pathH(t).point;return e.push(r,n),ih.LINE_TO}pathV(t,e){var{x:r,y:n}=sh.pathV(t).point;return e.push(r,n),ih.LINE_TO}pathC(t,e){var{point:r,controlPoint:n,currentPoint:i}=sh.pathC(t);e.push(r.x,r.y,n.x,n.y,i.x,i.y)}pathS(t,e){var{point:r,controlPoint:n,currentPoint:i}=sh.pathS(t);return e.push(r.x,r.y,n.x,n.y,i.x,i.y),ih.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:n}=sh.pathQ(t);e.push(r.x,r.y,n.x,n.y)}pathT(t,e){var{controlPoint:r,currentPoint:n}=sh.pathT(t);return e.push(r.x,r.y,n.x,n.y),ih.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:n,xAxisRotation:i,centp:a,a1:s,ad:o}=sh.pathA(t);return 0===n&&o>0&&(o-=2*Math.PI),1===n&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,s,o,i,n]}calcLength(t,e,r,n){var i=0,a=null,s=null,o=0;switch(r){case ih.LINE_TO:return this.getLineLength(t,e,n[0],n[1]);case ih.CURVE_TO:for(i=0,a=this.getPointOnCubicBezier(0,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),o=.01;o<=1;o+=.01)s=this.getPointOnCubicBezier(o,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return i;case ih.QUAD_TO:for(i=0,a=this.getPointOnQuadraticBezier(0,t,e,n[0],n[1],n[2],n[3]),o=.01;o<=1;o+=.01)s=this.getPointOnQuadraticBezier(o,t,e,n[0],n[1],n[2],n[3]),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return i;case ih.ARC:i=0;var u=n[4],h=n[5],c=n[4]+h,l=Math.PI/180;if(Math.abs(u-c)<l&&(l=Math.abs(u-c)),a=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],u,0),h<0)for(o=u-l;o>c;o-=l)s=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],o,0),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;else for(o=u+l;o<c;o+=l)s=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],o,0),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return s=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],c,0),i+=this.getLineLength(a.x,a.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,n,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(i-r)/(n-e+Su),u=Math.sqrt(t*t/(1+o*o));n<e&&(u*=-1);var h=o*u,c=null;if(n===e)c={x:a,y:s+h};else if((s-r)/(a-e+Su)===o)c={x:a+u,y:s+h};else{var l,f,g=this.getLineLength(e,r,n,i);if(g<Su)return null;var p=(a-e)*(n-e)+(s-r)*(i-r);l=e+(p/=g*g)*(n-e),f=r+p*(i-r);var d=this.getLineLength(a,s,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),n<e&&(u*=-1),c={x:l+u,y:f+(h=o*u)}}return c}getPointOnPath(t){var e=this.getPathLength(),r=0,n=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:i}=this;for(var a of i){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var s=t-r,o=0;switch(a.type){case ih.LINE_TO:n=this.getPointOnLine(s,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case ih.ARC:var u=a.points[4],h=a.points[5],c=a.points[4]+h;if(o=u+s/a.pathLength*h,h<0&&o<c||h>=0&&o>c)break;n=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case ih.CURVE_TO:(o=s/a.pathLength)>1&&(o=1),n=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case ih.QUAD_TO:(o=s/a.pathLength)>1&&(o=1),n=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(n)return n;break}r+=a.pathLength}return null}getLineLength(t,e,r,n){return Math.sqrt((r-t)*(r-t)+(n-e)*(n-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,n,i,a,s,o,u){return{x:o*Eu(t)+a*Cu(t)+n*Pu(t)+e*Nu(t),y:u*Eu(t)+s*Cu(t)+i*Pu(t)+r*Nu(t)}}getPointOnQuadraticBezier(t,e,r,n,i,a,s){return{x:a*Mu(t)+n*Ru(t)+e*_u(t),y:s*Mu(t)+i*Ru(t)+r*_u(t)}}getPointOnEllipticalArc(t,e,r,n,i,a){var s=Math.cos(a),o=Math.sin(a),u=r*Math.cos(i),h=n*Math.sin(i);return{x:t+(u*s-h*o),y:e+(u*o+h*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),n=e||.25,i=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==i||this.equidistantCache.precision!==n){this.equidistantCache={step:i,precision:n,points:[]};for(var a=0,s=0;s<=r;s+=n){var o=this.getPointOnPath(s),u=this.getPointOnPath(s+n);o&&u&&(a+=this.getLineLength(o.x,o.y,u.x,u.y))>=i&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:s}),a-=i)}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var n=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[n]||null}}t("TextPathElement",Lh);var Dh=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class jh extends ah{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var n=this.getHrefAttribute().getString();if(n){var i=n.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(n);t.images.push(this),i?this.loadSvg(n):this.loadImage(n),this.isSvg=i}}loadImage(t){var e=this;return ha(function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(n){console.error('Error while loading image "'.concat(t,'":'),n)}e.loaded=!0})()}loadSvg(t){var e=this;return ha(function*(){var r=Dh.exec(t);if(r){var n=r[5];"base64"===r[4]?e.image=atob(n):e.image=decodeURIComponent(n)}else try{var i=yield e.document.fetch(t),a=yield i.text();e.image=a}catch(s){console.error('Error while loading image "'.concat(t,'":'),s)}e.loaded=!0})()}renderChildren(t){var{document:e,image:r,loaded:n}=this,i=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(n&&r&&s&&o){if(t.save(),t.translate(i,a),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var h=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:o,desiredHeight:h.height}),this.loaded&&(void 0===h.complete||h.complete)&&t.drawImage(h,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),n=this.getStyle("height").getPixels("y");return new nh(t,e,t+r,e+n)}}t("ImageElement",jh);class Bh extends ah{constructor(){super(...arguments),this.type="symbol"}render(t){}}t("SymbolElement",Bh);class Fh{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return ha(function*(){try{var{document:n}=r,i=(yield n.canvg.parser.load(e)).getElementsByTagName("font");Array.from(i).forEach(e=>{var r=n.createElement(e);n.definitions[t]=r})}catch(a){console.error('Error while loading font "'.concat(e,'":'),a)}r.loaded=!0})()}}t("SVGFontLoader",Fh);class zh extends Zu{constructor(t,e,r){super(t,e,r),this.type="style";var n=au(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,""));n.split("}").forEach(e=>{var r=e.trim();if(r){var n=r.split("{"),i=n[0].split(","),a=n[1].split(";");i.forEach(e=>{var r=e.trim();if(r){var n=t.styles[r]||{};if(a.forEach(e=>{var r=e.indexOf(":"),i=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();i&&a&&(n[i]=new Vu(t,i,a))}),t.styles[r]=n,t.stylesSpecificity[r]=wu(r),"@font-face"===r){var i=n["font-family"].getString().replace(/"|'/g,"");n.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var r=lu(e);r&&new Fh(t).load(i,r)}})}}})}})}}t("StyleElement",zh),zh.parseExternalUrl=lu;class Uh extends ah{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var n=r;if("symbol"===r.type&&((n=new lh(e,null)).attributes.viewBox=new Vu(e,"viewBox",r.getAttribute("viewBox").getString()),n.attributes.preserveAspectRatio=new Vu(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),n.attributes.overflow=new Vu(e,"overflow",r.getAttribute("overflow").getString()),n.children=r.children,r.styles.opacity=new Vu(e,"opacity",this.calculateOpacity())),"svg"===n.type){var i=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);i.hasValue()&&(n.attributes.width=new Vu(e,"width",i.getString())),a.hasValue()&&(n.attributes.height=new Vu(e,"height",a.getString()))}var s=n.parent;n.parent=this,n.render(t),n.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return $u.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Hh(t,e,r,n,i,a){return t[r*n*4+4*e+a]}function Xh(t,e,r,n,i,a,s){t[r*n*4+4*e+a]=s}function Yh(t,e,r){return t[e]*r}function Wh(t,e,r,n){return e+Math.cos(t)*r+Math.sin(t)*n}t("UseElement",Uh);class qh extends Zu{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var n=uu(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var i=n[0];n=[.213+.787*i,.715-.715*i,.072-.072*i,0,0,.213-.213*i,.715+.285*i,.072-.072*i,0,0,.213-.213*i,.715-.715*i,.072+.928*i,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=n[0]*Math.PI/180;n=[Wh(a,.213,.787,-.213),Wh(a,.715,-.715,-.715),Wh(a,.072,-.072,.928),0,0,Wh(a,.213,-.213,.143),Wh(a,.715,.285,.14),Wh(a,.072,-.072,-.283),0,0,Wh(a,.213,-.213,-.787),Wh(a,.715,-.715,.715),Wh(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=n,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,n,i){for(var{includeOpacity:a,matrix:s}=this,o=t.getImageData(0,0,n,i),u=0;u<i;u++)for(var h=0;h<n;h++){var c=Hh(o.data,h,u,n,0,0),l=Hh(o.data,h,u,n,0,1),f=Hh(o.data,h,u,n,0,2),g=Hh(o.data,h,u,n,0,3),p=Yh(s,0,c)+Yh(s,1,l)+Yh(s,2,f)+Yh(s,3,g)+Yh(s,4,1),d=Yh(s,5,c)+Yh(s,6,l)+Yh(s,7,f)+Yh(s,8,g)+Yh(s,9,1),v=Yh(s,10,c)+Yh(s,11,l)+Yh(s,12,f)+Yh(s,13,g)+Yh(s,14,1),y=Yh(s,15,c)+Yh(s,16,l)+Yh(s,17,f)+Yh(s,18,g)+Yh(s,19,1);a&&(p=0,d=0,v=0,y*=g/255),Xh(o.data,h,u,n,0,0,p),Xh(o.data,h,u,n,0,1,d),Xh(o.data,h,u,n,0,2,v),Xh(o.data,h,u,n,0,3,y)}t.clearRect(0,0,n,i),t.putImageData(o,0,0)}}t("FeColorMatrixElement",qh);class Gh extends Zu{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,n=this.getAttribute("x").getPixels("x"),i=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!a&&!s){var o=new nh;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),n=Math.floor(o.x1),i=Math.floor(o.y1),a=Math.floor(o.width),s=Math.floor(o.height)}var u=this.removeStyles(e,Gh.ignoreStyles),h=r.createCanvas(n+a,i+s),c=h.getContext("2d");r.screen.setDefaults(c),this.renderChildren(c),new qh(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(c,0,0,n+a,i+s);var l=r.createCanvas(n+a,i+s),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=c.createPattern(h,"no-repeat"),f.fillRect(0,0,n+a,i+s),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,n+a,i+s),this.restoreStyles(e,u)}render(t){}}t("MaskElement",Gh),Gh.ignoreStyles=["mask","transform","clip-path"];var Qh=()=>{};class $h extends Zu{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:n,closePath:i}=t;r&&(r.beginPath=Qh,r.closePath=Qh),Reflect.apply(n,t,[]),this.children.forEach(n=>{if(void 0!==n.path){var a=void 0!==n.elementTransform?n.elementTransform():null;a||(a=$u.fromElement(e,n)),a&&a.apply(t),n.path(t),r&&(r.closePath=i),a&&a.unapply(t)}}),Reflect.apply(i,t,[]),t.clip(),r&&(r.beginPath=n,r.closePath=i)}render(t){}}t("ClipPathElement",$h);class Zh extends Zu{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:n}=this,i=e.getBoundingBox(t);if(i){var a=0,s=0;n.forEach(t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),s=Math.max(s,e)});var o=Math.floor(i.width),u=Math.floor(i.height),h=o+2*a,c=u+2*s;if(!(h<1||c<1)){var l=Math.floor(i.x),f=Math.floor(i.y),g=this.removeStyles(e,Zh.ignoreStyles),p=r.createCanvas(h,c),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-l+a,-f+s),e.render(d),n.forEach(t=>{"function"==typeof t.apply&&t.apply(d,0,0,h,c)}),t.drawImage(p,0,0,h,c,l-a,f-s,h,c),this.restoreStyles(e,g)}}}render(t){}}t("FilterElement",Zh),Zh.ignoreStyles=["filter","transform","clip-path"];class Kh extends Zu{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,n,i){}}t("FeDropShadowElement",Kh);class Jh extends Zu{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,n,i){}}t("FeMorphologyElement",Jh);class tc extends Zu{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,n,i){}}t("FeCompositeElement",tc);class ec extends Zu{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,n,i){var{document:a,blurRadius:s}=this,o=a.window?a.window.document.body:null,u=t.canvas;u.id=a.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),nu(u,e,r,n,i,s),o&&o.removeChild(u)}}t("FeGaussianBlurElement",ec);class rc extends Zu{constructor(){super(...arguments),this.type="title"}}t("TitleElement",rc);class nc extends Zu{constructor(){super(...arguments),this.type="desc"}}t("DescElement",nc);var ic={svg:lh,rect:fh,circle:gh,ellipse:ph,line:dh,polyline:vh,polygon:yh,path:sh,pattern:mh,marker:xh,defs:bh,linearGradient:Th,radialGradient:Oh,stop:Ah,animate:Eh,animateColor:Ch,animateTransform:Ph,font:Nh,"font-face":Mh,"missing-glyph":Rh,glyph:oh,text:uh,tspan:hh,tref:_h,a:Vh,textPath:Lh,image:jh,g:wh,symbol:Bh,style:zh,use:Uh,mask:Gh,clipPath:$h,filter:Zh,feDropShadow:Kh,feMorphology:Jh,feComposite:tc,feColorMatrix:qh,feGaussianBlur:ec,title:rc,desc:nc};function ac(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sc(){return sc=ha(function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,n)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,i,a)=>{n(a)},r.src=t})}),sc.apply(this,arguments)}class oc{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:n=oc.createCanvas,createImage:i=oc.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=n,this.createImage=this.bindCreateImage(i,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,n)=>t(r,"boolean"==typeof n?n:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=oc.elementTypes[e];return void 0!==r?new r(this,t):new Ku(this,t)}createTextNode(t){return new ch(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ac(Object(r),!0).forEach(function(e){Xs(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ac(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({document:this},t))}}function uc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uc(Object(r),!0).forEach(function(e){Xs(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}t("Document",oc),oc.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},oc.createImage=function(t){return sc.apply(this,arguments)},oc.elementTypes=ic;class cc{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new Uu(r),this.screen=new Bu(t,r),this.options=r;var n=new oc(this,r),i=n.createDocumentElement(e);this.document=n,this.documentElement=i}static from(t,e){var r=arguments;return ha(function*(){var n=r.length>2&&void 0!==r[2]?r[2]:{},i=new Uu(n),a=yield i.parse(e);return new cc(t,a,n)})()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=new Uu(r).parseFromString(e);return new cc(t,n,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return cc.from(t,e,hc(hc({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return cc.fromString(t,e,hc(hc({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return ha(function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(hc({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:n}=this;r.start(e,hc(hc({enableRedraw:!0},n),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}t({Canvg:cc,default:cc})}}});
