System.register(["./jspdf.es.min-legacy-DQftmmnr.js","./jspdf.plugin.autotable-legacy-fe79WZ9r.js","./vendor-legacy-BmDVBcfe.js","./index-legacy-D2-y4Zk0.js"],function(a,t){"use strict";var e,i,r,n,d,o,l,s,h,c,g,u,m,f;return{setters:[a=>{e=a.E},null,a=>{i=a.c,r=a.a,n=a.t,d=a.m,o=a.s,l=a.F,s=a.p,h=a.v,c=a.q,g=a.k,u=a.o,m=a.n},a=>{f=a._}],execute:function(){var t=document.createElement("style");t.textContent="#pantau-ngaji-cek[data-v-12b73d99],#pantau-ngaji-cek[data-v-12b73d99] *,#pantau-ngaji-cek[data-v-12b73d99] *:before,#pantau-ngaji-cek[data-v-12b73d99] *:after{box-sizing:border-box}#pantau-ngaji-cek[data-v-12b73d99]{font-family:system-ui,Arial,sans-serif;max-width:100%;margin:0 auto}h1[data-v-12b73d99]{text-align:center;margin:25px 0 10px;font-size:2em}.filter-item[data-v-12b73d99]{display:flex;flex-direction:column;gap:15px;box-sizing:border-box;background-color:#fff;padding:20px;box-shadow:0 4px 8px rgba(0,0,0,.1);border-radius:20px;margin-bottom:20px;width:100%;margin-left:auto;margin-right:auto}.filter-item div[data-v-12b73d99]{display:flex;flex-direction:column}.filter-item label[data-v-12b73d99]{margin-bottom:5px;font-weight:700}select[data-v-12b73d99],input[data-v-12b73d99]{border:1px solid #ccc;padding:10px;font-size:16px;background-color:#f9f9f9;-webkit-appearance:none;appearance:none;border-radius:20px;box-sizing:border-box;width:100%;height:45px}.table-container[data-v-12b73d99]{background:#fafbfc;border-radius:12px;box-shadow:0 2px 12px #eee;overflow-x:visible;max-width:100%;width:100%;margin:0 auto}.summary-container[data-v-12b73d99]{display:flex;justify-content:space-around;padding:15px;background:#f0f6ff;border-bottom:1px solid #e0e0e0;border-radius:12px 12px 0 0}.summary-item[data-v-12b73d99]{display:flex;flex-direction:column;align-items:center}.summary-label[data-v-12b73d99]{font-weight:700;font-size:.9em;color:#555}.summary-value[data-v-12b73d99]{font-size:1.4em;font-weight:700}table[data-v-12b73d99]{width:100%;border-collapse:collapse;table-layout:fixed}th[data-v-12b73d99],td[data-v-12b73d99]{border-bottom:1px solid #eeeeee;padding:10px 16px;text-align:left;overflow-x:auto;white-space:nowrap}th[data-v-12b73d99]{background:#f4f4f8;font-weight:700;cursor:pointer;white-space:nowrap}th span[data-v-12b73d99]{color:#888;font-size:1.1em}tr.hadir td[data-v-12b73d99]{background:#e8f8ed}tr.belum-hadir td[data-v-12b73d99]{background:#ffeaea}.danger-text[data-v-12b73d99]{color:#c33;font-weight:700}tfoot td[data-v-12b73d99]{background:#f7f7f7;border-top:2px solid #bbb}.button-container[data-v-12b73d99]{text-align:center;margin-top:20px;width:100%;margin-left:auto;margin-right:auto}@media only screen and (min-width: 768px){.filter-item[data-v-12b73d99],.table-container[data-v-12b73d99],.button-container[data-v-12b73d99]{box-sizing:border-box;max-width:768px;width:100%;margin-left:auto;margin-right:auto}}@media only screen and (max-width: 480px){.filter-item[data-v-12b73d99],.table-container[data-v-12b73d99],.button-container[data-v-12b73d99]{max-width:100%}}button[data-v-12b73d99]{border:1px solid #ccc;padding:10px 20px;font-size:16px;background-color:#f9f9f9;border-radius:20px;cursor:pointer;margin:0 10px}@media (max-width: 660px){.filter-item[data-v-12b73d99]{max-width:100%}th[data-v-12b73d99],td[data-v-12b73d99]{padding:7px 4px}button[data-v-12b73d99]{width:100%;margin:5px 0}}\n/*$vite$:1*/",document.head.appendChild(t);const p={id:"pantau-ngaji-cek"},b={style:{"margin-bottom":"10px","text-align":"center","font-weight":"bold"}},x={class:"filter-item"},k=["value"],y={key:0,class:"table-container"},v={class:"summary-container"},w={class:"summary-item"},S={class:"summary-value"},C={class:"summary-item"},j={class:"summary-value"},D={key:0},$={key:0},z={key:0},K={key:0},A={key:0},F={key:1,class:"danger-text"},_={key:0},L={key:1,class:"danger-text"},P={key:1,style:{"text-align":"center",padding:"2em"}},T={class:"button-container"};a("default",f({data:()=>({apiKey:"",filters:{acara:"",lokasi:"",tanggal:"",data:"",ranah:"",detailRanah:""},kelompokList:[],attendanceData:[],sortKey:"",sortOrder:"asc",sortCriteria:[],isLoading:!1}),computed:{uniqueRanah(){return[...new Set(this.kelompokList.map(a=>a.ranah))].sort()},mergedData(){const a={};for(const t of this.kelompokList)a[`${t.ranah}|${t.detail_ranah}`]={ranah:t.ranah,detail_ranah:t.detail_ranah,hadir:"-",jam_hadir:"belum-hadir",nama:[]};for(const t of this.attendanceData){const e=`${t.ranah}|${t.detail_ranah}`;a[e]&&("-"===a[e].hadir?(a[e].hadir=t.nama,a[e].nama=[t.nama]):(a[e].nama.push(t.nama),a[e].hadir=a[e].nama.join(", ")),"belum-hadir"===a[e].jam_hadir&&(a[e].jam_hadir=t.jam_hadir))}return Object.values(a)},filteredAndSortedData(){let a=this.mergedData;return this.filters.ranah&&(a=a.filter(a=>a.ranah===this.filters.ranah)),this.filters.detailRanah&&(a=a.filter(a=>a.detail_ranah.toLowerCase().includes(this.filters.detailRanah.toLowerCase()))),0===this.sortCriteria.length?a:[...a].sort((a,t)=>{const e="-"===a.hadir;if(e!==("-"===t.hadir))return e?1:-1;for(const i of this.sortCriteria){const e=i.key,r=i.order,n=(a[e]||"").toLowerCase(),d=(t[e]||"").toLowerCase();if(n!==d)return n<d?"asc"===r?-1:1:"asc"===r?1:-1}return 0})},totalHadir(){return this.filteredAndSortedData.filter(a=>"-"!==a.hadir).length},totalBelumHadir(){return this.filteredAndSortedData.filter(a=>"-"===a.hadir).length}},methods:{formatTanggalIndo(a){const t=new Date(a);return`${["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][t.getDay()]}, ${t.getDate()} ${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][t.getMonth()]} ${t.getFullYear()}`},async refresh(){this.isLoading=!0;try{await Promise.all([this.fetchKelompok(),this.fetchAttendance()])}finally{this.isLoading=!1}},async fetchKelompok(){const a=`/api/data/daerah/${encodeURIComponent(this.filters.data)}`,t=await fetch(a,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(!t.ok)throw new Error("Gagal fetch kelompok");const e=await t.json();this.kelompokList=e},async fetchAttendance(){const a=`/api/absen-pengajian/?tanggal=${this.filters.tanggal}&acara=${encodeURIComponent(this.filters.acara)}&lokasi=${encodeURIComponent(this.filters.lokasi)}`,t=await fetch(a,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(!t.ok)throw new Error("Gagal fetch absen");this.attendanceData=await t.json()},handleSort(a){const t=this.sortCriteria.findIndex(t=>t.key===a);if(-1!==t){const a=this.sortCriteria[t].order;this.sortCriteria[t].order="asc"===a?"desc":"asc"}else this.sortCriteria.push({key:a,order:"asc"});this.sortKey=a,this.sortOrder=this.sortCriteria.find(t=>t.key===a).order},filterTable(){},downloadPDF(){const a=new e({unit:"cm",format:"a4"});a.setFont("times","normal"),a.setFontSize(18),a.text("Daftar Pantauan Absen Kelompok",a.internal.pageSize.getWidth()/2,2,{align:"center"}),a.setFontSize(12),a.text(`Acara: ${this.filters.acara||"UMUM"}`,a.internal.pageSize.getWidth()/2,2.8,{align:"center"}),a.text(`Lokasi: ${this.filters.lokasi||"-"}`,a.internal.pageSize.getWidth()/2,3.3,{align:"center"}),a.text(`Tanggal: ${this.formatTanggalIndo(this.filters.tanggal)}`,a.internal.pageSize.getWidth()/2,3.8,{align:"center"}),a.autoTable({head:[["No","Desa","Kelompok","Hadir","Jam Hadir"]],body:this.filteredAndSortedData.map((a,t)=>[t+1,a.ranah,a.detail_ranah,a.hadir,a.jam_hadir]),startY:4.8,styles:{fontSize:10,cellPadding:.4},didDrawPage:t=>{const e=a.internal.pageSize.height,i=a.internal.pageSize.width;a.setFontSize(9),a.setTextColor(128),a.text(`Hal ${a.internal.getNumberOfPages()}`,i-1,e-.8,{align:"right"})}});const t=["PantauAbsen",this.filters.lokasi||"ALL",this.filters.acara||"UMUM",this.filters.tanggal||""].join("-");a.save(`${t}.pdf`)},handleTanggalChange(){this.refresh()}},watch:{"filters.acara"(a){document.title=`Pantauan Per Kelompok / Bidang - ${a||"UMUM"}`}},mounted(){const a=new URLSearchParams(window.location.search);this.apiKey=a.get("key")||"",this.filters.acara=a.get("acara")||"",this.filters.lokasi=a.get("lokasi")||"",this.filters.data=a.get("data")||"";const t=new Date;this.filters.tanggal=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`,this.refresh(),document.title=`Pantauan Per Kelompok / Bidang - ${this.filters.acara||"UMUM"}`}},[["render",function(a,t,e,f,U,M){return u(),i("div",p,[t[22]||(t[22]=r("h1",null,"-- VERSI BETA RELEASE --",-1)),t[23]||(t[23]=r("h1",null,"Pantauan Absen Per Kelompok",-1)),r("div",b,n(U.filters.acara||"UMUM"),1),r("section",null,[r("div",x,[r("div",null,[t[13]||(t[13]=r("label",{for:"desaFilter"},"Filter Desa:",-1)),d(r("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>U.filters.ranah=a),onChange:t[1]||(t[1]=(...a)=>M.filterTable&&M.filterTable(...a)),id:"desaFilter"},[t[12]||(t[12]=r("option",{value:""},"Semua",-1)),(u(!0),i(l,null,s(M.uniqueRanah,a=>(u(),i("option",{key:a,value:a},n(a),9,k))),128))],544),[[o,U.filters.ranah]])]),r("div",null,[t[14]||(t[14]=r("label",{for:"tanggalFilter"},"Filter Tanggal:",-1)),d(r("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=a=>U.filters.tanggal=a),onInput:t[3]||(t[3]=(...a)=>M.handleTanggalChange&&M.handleTanggalChange(...a)),id:"tanggalFilter"},null,544),[[h,U.filters.tanggal]])]),r("div",null,[t[15]||(t[15]=r("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),d(r("input",{type:"text","onUpdate:modelValue":t[4]||(t[4]=a=>U.filters.detailRanah=a),onInput:t[5]||(t[5]=(...a)=>M.filterTable&&M.filterTable(...a)),placeholder:"Cari kelompok...",id:"kelompokFilter"},null,544),[[h,U.filters.detailRanah]])])])]),U.isLoading?(u(),i("div",P,"Memuat data...")):(u(),i("div",y,[r("div",v,[r("div",w,[t[16]||(t[16]=r("span",{class:"summary-label"},"Total Hadir:",-1)),r("span",S,n(M.totalHadir),1)]),r("div",C,[t[17]||(t[17]=r("span",{class:"summary-label"},"Total Belum Hadir:",-1)),r("span",j,n(M.totalBelumHadir),1)])]),r("table",null,[r("thead",null,[r("tr",null,[r("th",{onClick:t[6]||(t[6]=a=>M.handleSort("ranah"))},[t[18]||(t[18]=c(" Desa ",-1)),"ranah"===U.sortKey?(u(),i("span",D,n("asc"===U.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:t[7]||(t[7]=a=>M.handleSort("detail_ranah"))},[t[19]||(t[19]=c(" Kelompok ",-1)),"detail_ranah"===U.sortKey?(u(),i("span",$,n("asc"===U.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:t[8]||(t[8]=a=>M.handleSort("hadir"))},[t[20]||(t[20]=c(" Hadir ",-1)),"hadir"===U.sortKey?(u(),i("span",z,n("asc"===U.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:t[9]||(t[9]=a=>M.handleSort("jam_hadir"))},[t[21]||(t[21]=c(" Jam Hadir ",-1)),"jam_hadir"===U.sortKey?(u(),i("span",K,n("asc"===U.sortOrder?"↑":"↓"),1)):g("",!0)])])]),r("tbody",null,[(u(!0),i(l,null,s(M.filteredAndSortedData,a=>(u(),i("tr",{key:a.ranah+"-"+a.detail_ranah,class:m({hadir:"-"!==a.hadir,"belum-hadir":"-"===a.hadir})},[r("td",null,n(a.ranah),1),r("td",null,n(a.detail_ranah),1),r("td",null,["-"!==a.hadir?(u(),i("span",A,n(a.hadir),1)):(u(),i("span",F,"-"))]),r("td",null,["-"!==a.hadir?(u(),i("span",_,n(a.jam_hadir),1)):(u(),i("span",L,"belum-hadir"))])],2))),128))])])])),r("div",T,[r("button",{onClick:t[10]||(t[10]=(...a)=>M.refresh&&M.refresh(...a))},"Refresh"),r("button",{onClick:t[11]||(t[11]=(...a)=>M.downloadPDF&&M.downloadPDF(...a))},"Download PDF")])])}],["__scopeId","data-v-12b73d99"]]))}}});
