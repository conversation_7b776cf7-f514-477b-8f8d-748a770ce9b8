import{E as D}from"./jspdf.es.min-BEhHETZb.js";import{a as F}from"./jspdf.plugin.autotable-Bc4jgkkc.js";import{c as d,a as r,t as o,m as h,s as b,F as S,p as y,v as w,q as f,k as m,o as g}from"./vendor-Xnl27S3x.js";import{_ as z}from"./index-CKhlfBVN.js";const C={name:"PantauAsrama",data(){return{attendanceData:[],filters:{sesi:"",ranah:"",kelompok:"",nama:"",tanggal:"",acara:"",lokasi:""},apiKey:"",sortKey:"",sortOrder:"asc"}},computed:{uniqueSesi(){return[...new Set(this.attendanceData.map(e=>e.sesi))].sort()},uniqueRanah(){return[...new Set(this.attendanceData.map(e=>e.ranah))].sort()},uniqueKelompok(){const e=this.filters.ranah?this.attendanceData.filter(t=>t.ranah===this.filters.ranah):this.attendanceData;return[...new Set(e.map(t=>t.detail_ranah))].sort()},filteredData(){const e=this.attendanceData.filter(t=>{const u=!this.filters.sesi||t.sesi===this.filters.sesi,s=!this.filters.ranah||t.ranah===this.filters.ranah,n=!this.filters.kelompok||t.detail_ranah===this.filters.kelompok,i=!this.filters.nama||t.nama.toLowerCase().includes(this.filters.nama.toLowerCase());return u&&s&&n&&i});return this.sortKey&&e.sort((t,u)=>{let s=this.sortKey==="index"?1:t[this.sortKey],n=this.sortKey==="index"?1:u[this.sortKey];return typeof s=="string"&&(s=s.toLowerCase()),typeof n=="string"&&(n=n.toLowerCase()),s<n?this.sortOrder==="asc"?-1:1:s>n?this.sortOrder==="asc"?1:-1:0}),e}},methods:{async fetchDataForDate(){const e="/api/absen-asramaan/?tanggal=".concat(this.filters.tanggal,"&acara=").concat(this.filters.acara,"&lokasi=").concat(this.filters.lokasi||"");try{const t=await fetch(e,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));this.attendanceData=await t.json()}catch(t){console.error("Error fetching data:",t)}},formatTanggal(e){const t=new Date(e),u=["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],s=["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"],n=u[t.getDay()],i=t.getDate(),a=s[t.getMonth()],l=t.getFullYear();return"".concat(n,", ").concat(i," ").concat(a," ").concat(l)},async downloadPDF(){const e=new D({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}});e.setFont("Times","normal"),e.setFontSize(20),e.text("Laporan Kehadiran Acara",e.internal.pageSize.getWidth()/2,2,{align:"center"}),e.setFontSize(16),e.text("".concat(this.filters.acara),e.internal.pageSize.getWidth()/2,2.8,{align:"center"}),e.setFontSize(20),e.text("".concat(this.filters.ranah||"Semua"),e.internal.pageSize.getWidth()/2,3.6,{align:"center"}),e.setFontSize(16);const t=this.formatTanggal(this.filters.tanggal);e.text("".concat(t),e.internal.pageSize.getWidth()/2,4.4,{align:"center"}),F(e,{head:[["No.","Sesi","Nama","Kelompok","Jam Hadir"]],body:this.filteredData.map((s,n)=>[n+1,s.sesi,s.nama,s.detail_ranah,s.jam_hadir]),startY:5.5,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},columnStyles:{0:{cellWidth:2},3:{cellWidth:5}},didDrawPage:s=>{const n=s.pageNumber,i=e.internal.pageSize.height,a=e.internal.pageSize.width;e.setFontSize(10);const l=i-1.5;e.setDrawColor(200,200,200),e.setLineWidth(.02),e.line(1,l,a-1,l);const p="ABSENSI ".concat(this.filters.acara," - ").concat(this.filters.ranah," - ").concat(this.filters.tanggal," - Halaman ").concat(n);e.text(p,a-1,l+.5,{align:"right"})}});const u="kehadiran-acara-".concat(this.filters.acara||"UMUM","-").concat(this.filters.sesi||"semua","-").concat(this.filters.ranah||"semua","-").concat(this.filters.nama||"semua","-").concat(this.filters.tanggal||"semua",".pdf");e.save(u)},async downloadAllPDF(){const e=new D({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}}),t=[...new Set(this.filteredData.map(s=>s.ranah))],u=(s=0)=>{if(s>=t.length){e.save("kehadiran-all.pdf");return}const n=t[s];s>0&&e.addPage(),e.setFont("Times","normal"),e.setFontSize(20),e.text("Laporan Kehadiran Acara",e.internal.pageSize.getWidth()/2,2,{align:"center"}),e.setFontSize(16),e.text("".concat(this.filters.acara||"UMUM"),e.internal.pageSize.getWidth()/2,2.8,{align:"center"}),e.setFontSize(20),e.text("".concat(n),e.internal.pageSize.getWidth()/2,3.6,{align:"center"}),e.setFontSize(16);const i=this.formatTanggal(this.filters.tanggal);e.text("".concat(i),e.internal.pageSize.getWidth()/2,4.4,{align:"center"});const a=this.filteredData.filter(l=>l.ranah===n);F(e,{head:[["No.","Sesi","Nama","Kelompok","Jam Hadir"]],body:a.map((l,p)=>[p+1,l.sesi,l.nama,l.detail_ranah,l.jam_hadir]),startY:5.5,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},columnStyles:{0:{cellWidth:2},3:{cellWidth:5}},didDrawPage:l=>{const v=e.internal.getCurrentPageInfo().pageNumber,T=e.internal.pageSize.height,c=e.internal.pageSize.width;e.setFontSize(10);const k=T-1.5;e.setDrawColor(200,200,200),e.setLineWidth(.02),e.line(1,k,c-1,k);const K="ABSENSI ".concat(this.filters.acara," - ").concat(n," - ").concat(this.filters.tanggal," - Halaman ").concat(v);e.text(K,c-1,k+.5,{align:"right"})}}),u(s+1)};u()},sortTable(e){this.sortKey===e?this.sortOrder=this.sortOrder==="asc"?"desc":"asc":(this.sortKey=e,this.sortOrder="asc")},openStatistics(){const e="stat-ngaji.html?key=".concat(this.apiKey,"&acara=").concat(this.filters.acara,"&lokasi=").concat(this.filters.lokasi);window.open(e,"_blank")},filterTable(){}},watch:{"filters.acara"(e){document.title="Pantauan Kehadiran Acara - ".concat(e||"UMUM")},"filters.ranah"(){this.filters.kelompok=""}},mounted(){const e=new Date;this.filters.tanggal="".concat(e.getFullYear(),"-").concat(String(e.getMonth()+1).padStart(2,"0"),"-").concat(String(e.getDate()).padStart(2,"0"));const t=new URLSearchParams(window.location.search);this.apiKey=t.get("key"),this.filters.acara=t.get("acara")||"",this.filters.lokasi=t.get("lokasi")||"",this.fetchDataForDate(),document.title="Pantauan Kehadiran - ".concat(this.filters.acara||"UMUM")}},M={id:"app"},P={style:{"margin-bottom":"15px","font-weight":"bold","text-align":"center"}},x={class:"filter-item"},N=["value"],U=["value"],W=["value"],A={class:"table-container"},O={id:"attendanceTable"},L={key:0},V={key:0},H={key:0},B={key:0},j={key:0},q={style:{display:"none"}},I={class:"button-container"};function J(e,t,u,s,n,i){return g(),d("div",M,[t[33]||(t[33]=r("h1",null,"Pantauan Kehadiran",-1)),r("div",P,o(n.filters.acara||"UMUM"),1),r("section",null,[r("div",x,[r("div",null,[t[19]||(t[19]=r("label",{for:"sesiFilter"},"Filter Sesi:",-1)),h(r("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>n.filters.sesi=a),onChange:t[1]||(t[1]=(...a)=>i.filterTable&&i.filterTable(...a)),id:"sesiFilter"},[t[18]||(t[18]=r("option",{value:""},"Semua",-1)),(g(!0),d(S,null,y(i.uniqueSesi,a=>(g(),d("option",{key:a,value:a},o(a),9,N))),128))],544),[[b,n.filters.sesi]])]),r("div",null,[t[21]||(t[21]=r("label",{for:"desaFilter"},"Filter Desa:",-1)),h(r("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>n.filters.ranah=a),onChange:t[3]||(t[3]=(...a)=>i.filterTable&&i.filterTable(...a)),id:"desaFilter"},[t[20]||(t[20]=r("option",{value:""},"Semua",-1)),(g(!0),d(S,null,y(i.uniqueRanah,a=>(g(),d("option",{key:a,value:a},o(a),9,U))),128))],544),[[b,n.filters.ranah]])]),r("div",null,[t[23]||(t[23]=r("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),h(r("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>n.filters.kelompok=a),onChange:t[5]||(t[5]=(...a)=>i.filterTable&&i.filterTable(...a)),id:"kelompokFilter"},[t[22]||(t[22]=r("option",{value:""},"Semua",-1)),(g(!0),d(S,null,y(i.uniqueKelompok,a=>(g(),d("option",{key:a,value:a},o(a),9,W))),128))],544),[[b,n.filters.kelompok]])]),r("div",null,[t[24]||(t[24]=r("label",{for:"tanggalFilter"},"Filter Tanggal:",-1)),h(r("input",{type:"date","onUpdate:modelValue":t[6]||(t[6]=a=>n.filters.tanggal=a),onInput:t[7]||(t[7]=(...a)=>i.fetchDataForDate&&i.fetchDataForDate(...a)),id:"tanggalFilter"},null,544),[[w,n.filters.tanggal]])]),r("div",null,[t[25]||(t[25]=r("label",{for:"namaFilter"},"Filter Nama:",-1)),h(r("input",{type:"text","onUpdate:modelValue":t[8]||(t[8]=a=>n.filters.nama=a),onInput:t[9]||(t[9]=(...a)=>i.filterTable&&i.filterTable(...a)),placeholder:"Cari nama...",id:"namaFilter"},null,544),[[w,n.filters.nama]])])])]),r("div",A,[r("table",O,[r("thead",null,[r("tr",null,[t[31]||(t[31]=r("th",null,"No.",-1)),r("th",{onClick:t[10]||(t[10]=a=>i.sortTable("sesi"))},[t[26]||(t[26]=f(" Sesi ",-1)),n.sortKey==="sesi"?(g(),d("span",L,o(n.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[11]||(t[11]=a=>i.sortTable("nama"))},[t[27]||(t[27]=f(" Nama ",-1)),n.sortKey==="nama"?(g(),d("span",V,o(n.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[12]||(t[12]=a=>i.sortTable("ranah"))},[t[28]||(t[28]=f(" Desa ",-1)),n.sortKey==="ranah"?(g(),d("span",H,o(n.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[13]||(t[13]=a=>i.sortTable("detail_ranah"))},[t[29]||(t[29]=f(" Kelompok ",-1)),n.sortKey==="detail_ranah"?(g(),d("span",B,o(n.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[14]||(t[14]=a=>i.sortTable("jam_hadir"))},[t[30]||(t[30]=f(" Jam Hadir ",-1)),n.sortKey==="jam_hadir"?(g(),d("span",j,o(n.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),t[32]||(t[32]=r("th",{style:{display:"none"}},"Tanggal",-1))])]),r("tbody",null,[(g(!0),d(S,null,y(i.filteredData,(a,l)=>(g(),d("tr",{key:l},[r("td",null,o(l+1),1),r("td",null,o(a.sesi),1),r("td",null,o(a.nama),1),r("td",null,o(a.ranah),1),r("td",null,o(a.detail_ranah),1),r("td",null,o(a.jam_hadir),1),r("td",q,o(a.tanggal),1)]))),128))])])]),r("div",I,[r("button",{onClick:t[15]||(t[15]=(...a)=>i.downloadPDF&&i.downloadPDF(...a))},"Download as PDF"),r("button",{onClick:t[16]||(t[16]=(...a)=>i.openStatistics&&i.openStatistics(...a))},"View Statistics"),r("button",{onClick:t[17]||(t[17]=(...a)=>i.downloadAllPDF&&i.downloadAllPDF(...a))},"Download All (By Ranah)")])])}const Q=z(C,[["render",J]]);export{Q as default};
