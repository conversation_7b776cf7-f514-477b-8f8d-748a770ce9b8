<template>
  <div class="page-wrapper">
    <div class="content-area">
      <div class="util-container">
        <div class="util-header">
          <h1 class="util-title">SISTEM ABSENSI SABILILLAH</h1>
          <p class="util-subtitle">Kumpulan Alat Absensi</p>
        </div>

        <!-- Login & API Key Management -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">🔐 Manajemen API Key</h2>
            <p class="tool-description">
              Login dan kelola API key untuk akses sistem
            </p>
          </div>

          <!-- Login Form -->
          <div v-if="!auth.isLoggedIn" class="auth-section">
            <div class="form-group">
              <label class="form-label">Username:</label>
              <input
                v-model="auth.username"
                type="text"
                placeholder="Masukkan username"
                class="form-input"
                @keyup.enter="login"
              />
            </div>

            <div class="form-group">
              <label class="form-label">Password:</label>
              <input
                v-model="auth.password"
                type="password"
                placeholder="Masukkan password"
                class="form-input"
                @keyup.enter="login"
              />
            </div>

            <button
              @click="login"
              class="action-button"
              :disabled="!auth.username || !auth.password || auth.isLoggingIn"
            >
              {{ auth.isLoggingIn ? "Masuk..." : "Masuk" }}
            </button>

            <div v-if="auth.loginError" class="error-message">
              {{ auth.loginError }}
            </div>
          </div>

          <!-- API Key Management -->
          <div v-else class="auth-section">
            <div class="user-info">
              <p class="welcome-text">
                Selamat datang,
                <strong>{{ auth.username }}</strong
                >!
              </p>
              <button @click="logout" class="action-button secondary small">
                Keluar
              </button>
            </div>

            <!-- Create API Key -->
            <div class="api-key-creation">
              <h3 class="section-subtitle">
                Buat Pasangan API Key (Read & Write)
              </h3>
              <p class="form-hint">
                Sistem akan membuat 2 API key sekaligus: Write-only (untuk absen
                ngaji/asrama) dan Read-only (untuk pantau/statistik)
              </p>

              <div class="form-group">
                <label class="form-label">Nama API Key:</label>
                <input
                  v-model="apiKeyForm.name"
                  type="text"
                  placeholder="Contoh: Absensi-Guru-2025 (akan dibuat -write dan -read)"
                  class="form-input"
                />
              </div>

              <div class="form-group">
                <label class="form-label">Berlaku Selama (hari):</label>
                <select v-model="apiKeyForm.expiresInDays" class="form-select">
                  <option value="1">1 Hari</option>
                  <option value="3">3 Hari</option>
                  <option value="7">Seminggu</option>
                  <option value="30">Sebulan</option>
                  <option value="90">3 Bulan</option>
                  <option value="365">Setahun</option>
                </select>
              </div>

              <button
                @click="createApiKey"
                class="action-button"
                :disabled="!apiKeyForm.name || apiKeyForm.isCreating"
              >
                {{
                  apiKeyForm.isCreating
                    ? "Membuat..."
                    : "Buat Read & Write Keys"
                }}
              </button>
            </div>

            <!-- API Keys List -->
            <div class="api-keys-list" v-if="validApiKeys.length > 0">
              <h3 class="section-subtitle">API Keys Valid Anda</h3>
              <div
                class="api-key-item"
                v-for="apiKey in validApiKeys"
                :key="apiKey.id"
              >
                <div class="api-key-info">
                  <div class="api-key-name">
                    {{ apiKey.name }}
                    <!-- Default badge -->
                    <span v-if="isDefault(apiKey)" class="api-key-default"
                      >DEFAULT</span
                    >
                  </div>
                  <div class="api-key-details">
                    <span
                      class="api-key-permission"
                      :class="apiKey.permission"
                      >{{ apiKey.permission }}</span
                    >
                    <span class="api-key-expires"
                      >Berlaku hingga: {{ formatDate(apiKey.expires_at) }}</span
                    >
                  </div>
                  <div class="api-key-value">
                    <input
                      :value="apiKey.key"
                      readonly
                      class="form-input api-key-input"
                      :ref="'apiKeyInput' + apiKey.id"
                    />
                    <button
                      @click="copyApiKey(apiKey)"
                      class="copy-button small"
                    >
                      {{ apiKey.copied ? "✓" : "📋" }}
                    </button>
                  </div>

                  <div class="api-key-actions">
                    <button
                      @click="setAsDefault(apiKey)"
                      class="action-button small"
                      :disabled="isDefault(apiKey)"
                    >
                      {{ isDefault(apiKey) ? "Default" : "Set as Default" }}
                    </button>
                    <button
                      @click="revokeApiKey(apiKey)"
                      class="action-button danger small"
                    >
                      Hapus
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Show expired keys separately if any -->
            <div class="expired-keys-list" v-if="expiredApiKeys.length > 0">
              <h3 class="section-subtitle">API Keys Kedaluwarsa</h3>
              <div class="expired-notice">
                <p>
                  {{ expiredApiKeys.length }} API key telah kedaluwarsa dan
                  perlu dihapus atau diperbaharui.
                </p>
              </div>
              <div
                class="api-key-item expired"
                v-for="apiKey in expiredApiKeys"
                :key="apiKey.id"
              >
                <div class="api-key-info">
                  <div class="api-key-name">
                    {{ apiKey.name }}
                  </div>
                  <div class="api-key-details">
                    <span
                      class="api-key-permission expired"
                      :class="apiKey.permission"
                      >{{ apiKey.permission }}</span
                    >
                    <span class="api-key-expires expired"
                      >Kedaluwarsa: {{ formatDate(apiKey.expires_at) }}</span
                    >
                  </div>
                </div>
                <button
                  @click="revokeApiKey(apiKey)"
                  class="action-button danger small"
                >
                  Hapus
                </button>
              </div>
            </div>

            <button
              @click="loadApiKeys"
              class="action-button secondary"
              :disabled="apiKeys.isLoading"
            >
              {{ apiKeys.isLoading ? "Memuat..." : "Muat Ulang API Keys" }}
            </button>
          </div>
        </div>

        <!-- URL Parameter Generator -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">🔗 Generator Parameter URL</h2>
            <p class="tool-description">
              Buat URL dengan parameter untuk semua halaman sistem
            </p>
          </div>

          <div class="form-group">
            <label class="form-label">Pilih Jenis:</label>
            <select v-model="urlGenerator.type" class="form-select">
              <option value="acara">🎯 Acara (Event)</option>
              <option value="asrama">🏠 Asrama (Dormitory)</option>
            </select>
            <small class="form-hint">
              Akan menghasilkan 3 URL sekaligus: Absensi, Pantauan, dan
              Statistik
            </small>
          </div>

          <!-- Unified Parameter Section -->
          <div class="param-section">
            <h3 class="section-subtitle">Parameter URL</h3>

            <div class="form-group">
              <label class="form-label">Nama Acara:</label>
              <input
                v-model="urlGenerator.extraParams.acara"
                type="text"
                placeholder="Contoh: Ngaji Muda-Mudi Daerah"
                class="form-input"
                title="Nama acara yang akan ditampilkan di halaman absensi"
              />
              <small class="form-hint">
                Gunakan dua spasi berturut-turut untuk membuat baris baru di
                tampilan
              </small>
            </div>

            <div class="form-group">
              <label class="form-label">Lokasi:</label>
              <input
                v-model="urlGenerator.extraParams.lokasi"
                type="text"
                placeholder="Contoh: Masjid Baitul Aziz"
                class="form-input"
                title="Lokasi pelaksanaan acara"
              />
            </div>

            <div class="form-group">
              <label class="form-label">Data Parameter:</label>
              <select
                v-model="urlGenerator.extraParams.data"
                class="form-select"
                title="Parameter data untuk mengambil daftar kelompok dari API"
              >
                <option value="" disabled>Pilih data parameter</option>
                <option
                  v-for="option in daerahOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.text }}
                </option>
              </select>
              <small class="form-hint">
                Parameter ini digunakan untuk mengambil data kelompok dari
                endpoint /api/data/daerah/
              </small>
            </div>

            <div class="form-group">
              <label class="form-label">Custom Placeholder (opsional):</label>
              <input
                v-model="urlGenerator.extraParams.ph"
                type="text"
                placeholder="Contoh: KELOMPOK"
                class="form-input"
                title="Teks placeholder kustom untuk input kelompok"
              />
            </div>

            <div v-if="showAsramaParams" class="form-group">
              <label class="form-label">Sesi (khusus asrama):</label>
              <select
                v-model="urlGenerator.extraParams.sesi"
                class="form-select"
                title="Sesi untuk absensi asrama"
              >
                <option value="" disabled>Pilih Sesi</option>
                <option
                  v-for="option in sesiOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.text }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">Filter Tanggal (opsional):</label>
              <input
                v-model="urlGenerator.extraParams.tanggal"
                type="date"
                class="form-input"
                title="Filter berdasarkan tanggal untuk pantauan dan statistik"
              />
            </div>

            <!-- Time Parameter - only for statistik ngaji -->
            <div v-if="urlGenerator.type === 'acara'" class="form-group">
              <label class="form-label"
                >Parameter Waktu Statistik (opsional):</label
              >
              <input
                v-model="urlGenerator.extraParams.time"
                type="time"
                placeholder="Waktu Referensi"
                class="form-input"
                title="Waktu referensi untuk statistik ngaji"
              />
              <small class="form-hint">
                Parameter waktu khusus untuk halaman statistik ngaji
              </small>
            </div>
          </div>

          <!-- Generate URLs Button -->
          <div class="form-group">
            <button @click="generateUrls" class="action-button primary">
              🚀 Generate URLs
            </button>
            <small class="form-hint">
              Generate URLs dan kirim ke QR Code Generator & URL Shortener
            </small>
          </div>

          <!-- Multiple URL Display -->
          <div
            class="generated-urls"
            v-if="showGeneratedUrls && generatedUrls.length > 0"
          >
            <label class="form-label">URL yang Dihasilkan:</label>

            <div
              v-for="(urlData, index) in generatedUrls"
              :key="index"
              class="url-item"
            >
              <label class="url-label">
                {{ urlData.label }}:
                <span
                  class="key-type-badge"
                  :style="{
                    backgroundColor: getKeyTypeBadge(urlData.label).color,
                  }"
                >
                  {{ getKeyTypeBadge(urlData.label).text }}
                </span>
              </label>
              <div class="url-display">
                <input
                  :value="urlData.url"
                  readonly
                  class="form-input url-input"
                  :ref="`generatedUrlInput${index}`"
                />
                <button
                  @click="copyUrl(urlData.url, index)"
                  class="copy-button"
                >
                  {{ copyStatus[index] ? "✓" : "📋" }}
                </button>
              </div>
            </div>

            <div class="url-actions">
              <button @click="copyAllUrls" class="action-button">
                📋 Copy Semua URL
              </button>
            </div>
          </div>
        </div>

        <!-- QR Code Generator -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">📱 Generator QR Code</h2>
            <p class="tool-description">
              Buat QR code untuk URL absensi (ngaji atau asrama)
            </p>
          </div>

          <!-- Quick Action for Generated URLs -->
          <div class="quick-action-section">
            <button
              @click="generateQRForAbsen"
              class="quick-action-button"
              :disabled="!hasAbsenUrl"
            >
              🚀 Generate QR for Absen
            </button>
            <small class="form-hint">
              Otomatis buat QR code untuk URL absensi yang telah dihasilkan
            </small>
          </div>

          <div class="form-group">
            <label class="form-label">Text Kustom untuk PDF:</label>
            <input
              v-model="qrGenerator.customText"
              type="text"
              placeholder="Contoh: Kajian Pagi Sesi 1"
              class="form-input"
            />
            <small class="form-hint">
              Text ini akan muncul di bawah "ABSENSI" pada PDF
            </small>
          </div>

          <div class="form-group">
            <label class="form-label">Ukuran QR Code:</label>
            <select v-model="qrGenerator.size" class="form-select">
              <option value="200">Kecil (200x200)</option>
              <option value="300">Sedang (300x300)</option>
              <option value="400">Besar (400x400)</option>
            </select>
          </div>

          <div class="qr-result" v-if="qrCodeUrl">
            <div class="qr-display">
              <img
                :src="qrCodeUrl"
                :alt="'QR Code untuk ' + qrGenerator.url"
                class="qr-image"
              />
            </div>

            <div class="qr-actions">
              <button @click="downloadQR" class="action-button secondary">
                💾 Download QR Code
              </button>
              <button
                @click="generatePDF"
                class="action-button"
                :disabled="!qrGenerator.customText.trim()"
              >
                📄 Generate PDF (A5)
              </button>
            </div>
          </div>
        </div>

        <!-- URL Shortener -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">✂️ Pemendek URL</h2>
            <p class="tool-description">
              Buat URL pendek untuk kemudahan berbagi
            </p>
          </div>

          <!-- Quick Action for Generated URLs -->
          <div class="quick-action-section">
            <button
              @click="shortenAllGeneratedUrls"
              class="quick-action-button"
              :disabled="!generatedUrls.length || isShortening"
            >
              {{ isShortening ? "Memproses..." : "🚀 Shorten All URLs" }}
            </button>
            <small class="form-hint">
              Otomatis pendekkan semua URL (Absen, Pantau, Statistik) yang telah
              dihasilkan
            </small>
          </div>

          <div class="shortened-result" v-if="shortenedUrls.length > 0">
            <label class="form-label">URLs Pendek:</label>
            <div
              v-for="(urlData, index) in shortenedUrls"
              :key="index"
              class="url-item"
            >
              <label class="url-label">
                {{ urlData.label }}:
                <span
                  class="key-type-badge"
                  :style="{
                    backgroundColor: getUrlTypeBadge(urlData.type).color,
                  }"
                >
                  {{ getUrlTypeBadge(urlData.type).text }}
                </span>
              </label>
              <div class="url-display">
                <input
                  :value="urlData.url"
                  readonly
                  class="form-input url-input"
                  :ref="`shortenedUrlInput${index}`"
                />
                <button @click="copyShortUrl(index)" class="copy-button">
                  {{ shortUrlCopied[index] ? "✓" : "📋" }}
                </button>
              </div>
            </div>
            <div class="url-actions">
              <button @click="copyAllShortUrls" class="action-button">
                📋 Copy Semua URL Pendek
              </button>
            </div>
          </div>
        </div>

        <!-- Additional Tools -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">🛠️ Additional Tools</h2>
            <p class="tool-description">
              Tools tambahan untuk QR code dan URL shortener dengan input manual
            </p>
          </div>

          <!-- Manual URL Input -->
          <div class="form-group">
            <label class="form-label">URL Manual:</label>
            <input
              v-model="additionalTools.manualUrl"
              type="text"
              placeholder="Masukkan URL yang ingin diproses"
              class="form-input"
            />
          </div>

          <!-- QR Code Generation -->
          <div class="additional-tool-section">
            <h3 class="section-subtitle">📱 QR Code Generator</h3>

            <div class="form-group">
              <label class="form-label">Ukuran QR Code:</label>
              <select v-model="additionalTools.qrSize" class="form-select">
                <option value="200">Kecil (200x200)</option>
                <option value="300">Sedang (300x300)</option>
                <option value="400">Besar (400x400)</option>
              </select>
            </div>

            <button
              @click="generateQRFromManualInput"
              class="action-button"
              :disabled="!additionalTools.manualUrl.trim()"
            >
              Buat QR Code
            </button>

            <div class="qr-result" v-if="additionalTools.qrCodeUrl">
              <div class="qr-display">
                <img
                  :src="additionalTools.qrCodeUrl"
                  :alt="'QR Code untuk ' + additionalTools.manualUrl"
                  class="qr-image"
                />
              </div>

              <!-- Custom text for PDF -->
              <div class="form-group">
                <label class="form-label">Teks Kustom untuk PDF:</label>
                <input
                  v-model="additionalTools.customText"
                  type="text"
                  placeholder="Masukkan teks yang akan ditampilkan di PDF"
                  class="form-input"
                />
              </div>

              <div class="qr-actions">
                <button
                  @click="downloadAdditionalQR"
                  class="action-button secondary"
                >
                  💾 Download QR Code
                </button>
                <button
                  @click="generateAdditionalPDF"
                  class="action-button"
                  :disabled="!additionalTools.customText.trim()"
                >
                  📄 Generate PDF
                </button>
              </div>
            </div>
          </div>

          <!-- URL Shortener -->
          <div class="additional-tool-section">
            <h3 class="section-subtitle">✂️ URL Shortener</h3>

            <button
              @click="shortenManualUrl"
              class="action-button"
              :disabled="
                !additionalTools.manualUrl.trim() ||
                additionalTools.isProcessing
              "
            >
              {{
                additionalTools.isProcessing ? "Memproses..." : "Pendekkan URL"
              }}
            </button>

            <div
              class="shortened-result"
              v-if="additionalTools.shortenedManualUrl"
            >
              <label class="form-label">URL Pendek:</label>
              <div class="url-display">
                <input
                  :value="additionalTools.shortenedManualUrl"
                  readonly
                  class="form-input url-input"
                  ref="additionalShortenedUrlInput"
                />
                <button @click="copyAdditionalShortUrl" class="copy-button">
                  {{ additionalTools.copied ? "✓" : "📋" }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Data Management -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">📊 Manajemen Data</h2>
            <p class="tool-description">
              Kelola data sistem dan test API endpoints
            </p>
          </div>

          <div class="form-group">
            <label class="form-label">Pilih Endpoint Data:</label>
            <select v-model="dataManager.selectedEndpoint" class="form-select">
              <option value="">Pilih endpoint...</option>
              <optgroup label="Data Master">
                <option value="data/daerah">Daerah (/api/data/daerah/)</option>
                <option value="data/sesi">Sesi (/api/data/sesi/)</option>
                <option value="data/materi">Materi (/api/data/materi/)</option>
                <option value="data/hobi">Hobi (/api/data/hobi/)</option>
                <option value="data/kelas-sekolah">
                  Kelas Sekolah (/api/data/kelas-sekolah/)
                </option>
              </optgroup>
              <optgroup label="Wilayah">
                <option value="wilayah">Wilayah (/api/wilayah/)</option>
              </optgroup>
              <optgroup label="Biodata">
                <option value="biodata">Biodata (/api/biodata/)</option>
              </optgroup>
            </select>
          </div>

          <div v-if="dataManager.selectedEndpoint" class="form-group">
            <label class="form-label">Parameter Tambahan (opsional):</label>
            <input
              v-model="dataManager.parameter"
              type="text"
              placeholder="Contoh: medan-timur-1 (untuk endpoint daerah)"
              class="form-input"
            />
            <small class="form-hint">
              Beberapa endpoint memerlukan parameter tambahan seperti ID atau
              nama daerah
            </small>
          </div>

          <button
            @click="fetchEndpointData"
            class="action-button"
            :disabled="!dataManager.selectedEndpoint || dataManager.isLoading"
          >
            {{ dataManager.isLoading ? "Memuat..." : "Ambil Data" }}
          </button>

          <div v-if="dataManager.error" class="error-message">
            {{ dataManager.error }}
          </div>

          <div
            v-if="dataManager.data && dataManager.data.length > 0"
            class="data-result"
          >
            <h3 class="section-subtitle">
              Data dari {{ dataManager.selectedEndpoint }}
            </h3>
            <div class="data-summary">
              <p>
                <strong>Total records:</strong>
                {{ dataManager.data.length }}
              </p>
              <button @click="exportData" class="action-button secondary small">
                📥 Export JSON
              </button>
            </div>

            <div class="data-table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th v-for="(_, key) in dataManager.data[0]" :key="key">
                      {{ key }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, index) in dataManager.data.slice(0, 10)"
                    :key="index"
                  >
                    <td v-for="(value, key) in item" :key="key">
                      {{ value }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <p v-if="dataManager.data.length > 10" class="data-note">
                Menampilkan 10 dari
                {{ dataManager.data.length }} records. Export untuk melihat
                semua data.
              </p>
            </div>
          </div>
        </div>

        <!-- API Testing -->
        <div class="tool-section">
          <div class="tool-header">
            <h2 class="tool-title">🧪 Test API Endpoints</h2>
            <p class="tool-description">Test dan debug API endpoints sistem</p>
          </div>

          <div class="form-group">
            <label class="form-label">Pilih Endpoint untuk Test:</label>
            <select v-model="apiTester.selectedEndpoint" class="form-select">
              <option value="">Pilih endpoint...</option>
              <optgroup label="Absensi">
                <option value="absen-pengajian">
                  Absensi Pengajian (/api/absen-pengajian/)
                </option>
                <option value="absen-asramaan">
                  Absensi Asramaan (/api/absen-asramaan/)
                </option>
              </optgroup>
              <optgroup label="URL Shortener">
                <option value="url">URL Shortener (/api/url/)</option>
              </optgroup>
            </select>
          </div>

          <div v-if="apiTester.selectedEndpoint" class="form-group">
            <label class="form-label">Method:</label>
            <select v-model="apiTester.method" class="form-select">
              <option value="GET">GET</option>
              <option value="POST">POST</option>
            </select>
          </div>

          <div
            v-if="apiTester.selectedEndpoint && apiTester.method === 'POST'"
            class="form-group"
          >
            <label class="form-label">Test Data (JSON):</label>
            <textarea
              v-model="apiTester.testDataJson"
              class="form-textarea"
              rows="6"
              placeholder='{"nama": "Test User", "acara": "Test Event"}'
            ></textarea>
            <small class="form-hint">
              Masukkan data JSON untuk testing POST requests
            </small>
          </div>

          <div
            v-if="apiTester.selectedEndpoint && apiTester.method === 'GET'"
            class="form-group"
          >
            <label class="form-label">Query Parameters:</label>
            <input
              v-model="apiTester.queryParams"
              type="text"
              placeholder="tanggal=2025-01-01&acara=Test"
              class="form-input"
            />
            <small class="form-hint">
              Format: param1=value1&param2=value2
            </small>
          </div>

          <button
            @click="testApiEndpoint"
            class="action-button"
            :disabled="!apiTester.selectedEndpoint || apiTester.isLoading"
          >
            {{ apiTester.isLoading ? "Testing..." : "Test Endpoint" }}
          </button>

          <div v-if="apiTester.error" class="error-message">
            {{ apiTester.error }}
          </div>

          <div v-if="apiTester.response" class="api-response">
            <h3 class="section-subtitle">Response</h3>
            <div class="response-info">
              <span
                class="response-status"
                :class="apiTester.response.ok ? 'success' : 'error'"
              >
                Status: {{ apiTester.response.status }}
              </span>
              <span class="response-time">
                Time: {{ apiTester.response.time }}ms
              </span>
            </div>
            <pre class="response-body">{{
              JSON.stringify(apiTester.response.data, null, 2)
            }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// Note: This component requires jsPDF for PDF generation
// Install with: npm install jspdf
export default {
  name: "Util",
  computed: {
    isValidUrl() {
      if (!this.qrGenerator.url) return false;

      // Only allow absen URLs (ngaji or asrama), not pantau or statistik
      const url = this.qrGenerator.url;
      const isNgajiUrl =
        url.includes("/ngaji?") ||
        url.includes("/ngaji") ||
        url.endsWith("/ngaji");
      const isAsramaUrl =
        url.includes("/asrama?") ||
        url.includes("/asrama") ||
        url.endsWith("/asrama");

      console.log("🔍 isValidUrl check for:", url);
      console.log("🔍 isNgajiUrl:", isNgajiUrl);
      console.log("🔍 isAsramaUrl:", isAsramaUrl);

      return isNgajiUrl || isAsramaUrl;
    },

    hasAbsenUrl() {
      // Check if QR generator has a valid absen URL
      if (!this.qrGenerator.url) return false;
      const url = this.qrGenerator.url;
      const isAbsenUrl = url.includes("/ngaji") || url.includes("/asrama");
      const isPantauOrStatUrl =
        url.includes("/pantau") || url.includes("/stat");
      return isAbsenUrl && !isPantauOrStatUrl;
    },

    canGenerateUrls() {
      // Always allow button to be clicked - validation will happen inside the method
      return true;
    },
  },
  data() {
    return {
      // Authentication storage key
      STORAGE_KEY: "util_auth",

      // Authentication
      auth: {
        isLoggedIn: false,
        isLoggingIn: false,
        username: "",
        password: "",
        accessToken: "",
        loginError: "",
      },

      // API Key Management (auto-attached in buildUrl)
      apiKeys: [],
      defaultReadKey: null, // { id, name, value } or null
      defaultWriteKey: null, // { id, name, value } or null
      apiKeyForm: {
        name: "",
        expiresInDays: "30",
        isCreating: false,
      },

      // Hardcoded options for the Data Parameter dropdown
      daerahOptions: [
        { value: "medan-barat-tex", text: "Tex" },
        { value: "medan-barat-ppg", text: "Materi PPG" },
        { value: "medan-barat-smb", text: "Umum" },
        { value: "medan-barat-mbg", text: "MT/MS" },
      ],

      sesiOptions: [
        { value: "asrama-normal", text: "Pagi Siang Malam" },
        { value: "asrama-padat", text: "Subuh Pagi Siang Malam" },
        { value: "mtms-medan-barat", text: "MT/MS" },
      ],

      // URL Generator
      urlGenerator: {
        type: "acara",
        extraParams: {
          acara: "",
          lokasi: "",
          data: "",
          ph: "",
          sesi: "",
          tanggal: "",
          time: "", // Only for statistik ngaji
        },
      },
      showGeneratedUrls: false,

      copyStatus: [false, false, false], // For 3 URLs

      // QR Code Generator
      qrGenerator: {
        url: "",
        size: "300",
        customText: "",
      },
      qrCodeUrl: null,

      // URL Shortener
      urlShortener: {
        targetType: "absen",
        longUrl: "",
        selectedUrls: [], // Array of URLs to shorten
      },
      shortenedUrls: [], // Array of shortened URLs
      isShortening: false,
      shortUrlCopied: [], // Array of copy states

      // Console logging flags to prevent spam
      loggedApiKeyUsage: {
        writeKey: false,
        readKey: false,
        writeKeyWarning: false,
        readKeyWarning: false,
      },

      // DOM warning flags to prevent repeated DOM operations
      domWarningShown: {
        writeKeyWarning: false,
        readKeyWarning: false,
      },

      // Data Management
      dataManager: {
        selectedEndpoint: "",
        parameter: "",
        data: [],
        isLoading: false,
        error: null,
      },

      // API Testing
      apiTester: {
        selectedEndpoint: "",
        method: "GET",
        testDataJson: "",
        queryParams: "",
        response: null,
        isLoading: false,
        error: null,
      },

      // Additional Tools (independent of API key system)
      additionalTools: {
        manualUrl: "",
        qrSize: "300",
        shortenedManualUrl: "",
        isProcessing: false,
        qrCodeUrl: null,
        customText: "",
      },

      // Base URL
      baseUrl: window.location.origin,
    };
  },
  computed: {
    showAttendanceParams() {
      return (
        this.urlGenerator.type === "acara" ||
        this.urlGenerator.type === "asrama"
      );
    },

    showMonitoringParams() {
      return (
        this.urlGenerator.type === "acara" ||
        this.urlGenerator.type === "asrama"
      );
    },

    showStatisticsParams() {
      return (
        this.urlGenerator.type === "acara" ||
        this.urlGenerator.type === "asrama"
      );
    },

    showAsramaParams() {
      return this.urlGenerator.type === "asrama";
    },

    generatedUrls() {
      if (!this.urlGenerator.type) return [];

      const baseUrl = this.baseUrl;
      const type = this.urlGenerator.type;
      const urls = [];

      if (type === "acara") {
        // Generate ngaji, pantau-ngaji, stat-ngaji URLs
        urls.push({
          label: "URL Absensi Acara",
          url: this.buildUrl(baseUrl + "/ngaji", "attendance"),
        });
        urls.push({
          label: "URL Pantauan Acara",
          url: this.buildUrl(baseUrl + "/pantau-ngaji", "monitoring"),
        });
        urls.push({
          label: "URL Statistik Acara",
          url: this.buildUrl(baseUrl + "/stat-ngaji", "statistics"),
        });
      } else if (type === "asrama") {
        // Generate asrama, pantau-asrama, stat-asrama URLs
        urls.push({
          label: "URL Absensi Asrama",
          url: this.buildUrl(baseUrl + "/asrama", "attendance"),
        });
        urls.push({
          label: "URL Pantauan Asrama",
          url: this.buildUrl(baseUrl + "/pantau-asrama", "monitoring"),
        });
        urls.push({
          label: "URL Statistik Asrama",
          url: this.buildUrl(baseUrl + "/stat-asrama", "statistics"),
        });
      }

      return urls;
    },

    // Keep legacy computed property for backward compatibility with QR generator
    generatedUrl() {
      if (this.generatedUrls.length > 0) {
        return this.generatedUrls[0].url; // Return first URL (attendance)
      }
      return "";
    },

    validApiKeys() {
      if (!Array.isArray(this.apiKeys)) return [];
      const now = new Date();
      return this.apiKeys.filter((apiKey) => {
        if (!apiKey.expires_at) return true; // No expiry date means it's valid
        const expiryDate = new Date(apiKey.expires_at);
        return expiryDate > now;
      });
    },

    expiredApiKeys() {
      if (!Array.isArray(this.apiKeys)) return [];
      const now = new Date();
      return this.apiKeys.filter((apiKey) => {
        if (!apiKey.expires_at) return false; // No expiry date means it's valid
        const expiryDate = new Date(apiKey.expires_at);
        return expiryDate <= now;
      });
    },
  },
  watch: {
    // Auto-populate QR generator with first generated URL
    generatedUrl(newUrl) {
      if (newUrl && !this.qrGenerator.url) {
        this.qrGenerator.url = newUrl;
      }
    },
  },
  methods: {
    // Authentication Methods
    async login() {
      if (!this.auth.username || !this.auth.password) return;

      this.auth.isLoggingIn = true;
      this.auth.loginError = "";

      try {
        const response = await fetch("/api/auth/login/", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            username: this.auth.username,
            password: this.auth.password,
          }),
        });

        if (!response.ok) {
          let errorMessage = "Login gagal";

          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorMessage;
          } catch (parseError) {
            // Server returned HTML or non-JSON response
            errorMessage = `Server error (${response.status}): ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        this.auth.accessToken = data.access;
        this.auth.isLoggedIn = true;
        this.auth.password = ""; // Clear password for security

        // Store authentication data
        localStorage.setItem(
          this.STORAGE_KEY,
          JSON.stringify({
            username: this.auth.username,
            accessToken: this.auth.accessToken,
          }),
        );

        // Load existing API keys
        await this.loadApiKeys();
      } catch (error) {
        console.error("Login error:", error);
        this.auth.loginError =
          error.message || "Gagal login. Periksa username dan password Anda.";
      } finally {
        this.auth.isLoggingIn = false;
      }
    },

    logout() {
      // Clear stored authentication
      localStorage.removeItem(this.STORAGE_KEY);

      this.auth = {
        isLoggedIn: false,
        isLoggingIn: false,
        username: "",
        password: "",
        accessToken: "",
        loginError: "",
      };
      this.apiKeys = [];
      this.apiKeyForm = {
        name: "",
        expiresInDays: "30",
        isCreating: false,
      };
    },

    /*
     * AUTOMATIC API KEY SYSTEM
     *
     * This system automatically injects the correct API key into generated URLs:
     *
     * WRITE_ONLY keys are used for:
     * - "URL Absensi Acara" (/ngaji)
     * - "URL Absensi Asrama" (/asrama)
     *
     * READ_ONLY keys are used for:
     * - "URL Pantauan Acara" (/pantau-ngaji)
     * - "URL Pantauan Asrama" (/pantau-asrama)
     * - "URL Statistik Acara" (/stat-ngaji)
     * - "URL Statistik Asrama" (/stat-asrama)
     *
     * Users can:
     * 1. Create API keys with specific permissions
     * 2. Set one key of each type as "default"
     * 3. Generate URLs that automatically include the appropriate key
     *
     * No more manual copying of keys between sections!
     */

    // Helper methods for default API keys
    isDefault(key) {
      return (
        (this.defaultReadKey && this.defaultReadKey.id === key.id) ||
        (this.defaultWriteKey && this.defaultWriteKey.id === key.id)
      );
    },

    setAsDefault(key) {
      if (key.permission === "read_only") {
        this.defaultReadKey = key;
        console.log(`✅ Set "${key.name}" as default READ_ONLY key`);
        // Reset logging flags for read key
        this.loggedApiKeyUsage.readKey = false;
        this.loggedApiKeyUsage.readKeyWarning = false;
        this.domWarningShown.readKeyWarning = false;
      }
      if (key.permission === "write_only") {
        this.defaultWriteKey = key;
        console.log(`✅ Set "${key.name}" as default WRITE_ONLY key`);
        // Reset logging flags for write key
        this.loggedApiKeyUsage.writeKey = false;
        this.loggedApiKeyUsage.writeKeyWarning = false;
        this.domWarningShown.writeKeyWarning = false;
      }
      // Persist for the next visit
      localStorage.setItem(
        "defaultReadKey",
        JSON.stringify(this.defaultReadKey),
      );
      localStorage.setItem(
        "defaultWriteKey",
        JSON.stringify(this.defaultWriteKey),
      );

      // Show user feedback
      const permissionText = key.permission.replace("_", " ").toUpperCase();
      console.log(`✅ "${key.name}" telah dijadikan default ${permissionText} key`);
    },

    // Helper to get key type for URL based on its label
    getKeyTypeForUrl(urlLabel) {
      if (urlLabel.includes("Absensi")) {
        return "write_only";
      } else if (
        urlLabel.includes("Pantauan") ||
        urlLabel.includes("Statistik")
      ) {
        return "read_only";
      }
      return "read_only";
    },

    // Helper to get key type badge color
    getKeyTypeBadge(urlLabel) {
      const keyType = this.getKeyTypeForUrl(urlLabel);
      return {
        type: keyType,
        color: keyType === "write_only" ? "#ff9500" : "#34c759",
        text: keyType === "write_only" ? "WRITE" : "READ",
      };
    },

    // Helper to get URL type badge for shortened URLs
    getUrlTypeBadge(urlType) {
      switch (urlType) {
        case "absensi":
          return {
            color: "#007aff",
            text: "ABSENSI",
          };
        case "pantau":
          return {
            color: "#ff9500",
            text: "PANTAU",
          };
        case "statistik":
          return {
            color: "#34c759",
            text: "STATISTIK",
          };
        default:
          return {
            color: "#8e8e93",
            text: "UNKNOWN",
          };
      }
    },

    // Helper to determine URL type from label
    getUrlTypeFromLabel(label) {
      if (label.includes("Absensi")) {
        return "absensi";
      } else if (label.includes("Pantauan")) {
        return "pantau";
      } else if (label.includes("Statistik")) {
        return "statistik";
      }
      return "unknown";
    },

    validateDefaultKeys() {
      // Check if current default keys are still valid
      const validKeys = this.validApiKeys;

      if (this.defaultReadKey) {
        const isStillValid = validKeys.some(
          (key) =>
            key.id === this.defaultReadKey.id && key.permission === "read_only",
        );
        if (!isStillValid) {
          console.warn(
            `🗑️ Default READ_ONLY key "${this.defaultReadKey.name}" is no longer valid - clearing`,
          );
          this.defaultReadKey = null;
          localStorage.removeItem("defaultReadKey");
        }
      }

      if (this.defaultWriteKey) {
        const isStillValid = validKeys.some(
          (key) =>
            key.id === this.defaultWriteKey.id &&
            key.permission === "write_only",
        );
        if (!isStillValid) {
          console.warn(
            `🗑️ Default WRITE_ONLY key "${this.defaultWriteKey.name}" is no longer valid - clearing`,
          );
          this.defaultWriteKey = null;
          localStorage.removeItem("defaultWriteKey");
        }
      }
    },

    // API Key Management Methods
    async createApiKey() {
      if (!this.apiKeyForm.name || !this.auth.accessToken) return;

      this.apiKeyForm.isCreating = true;

      try {
        const permissions = ["write_only", "read_only"];
        const createdKeys = [];

        for (const perm of permissions) {
          const suffix = perm === "write_only" ? "write" : "read";
          const payload = {
            name: `${this.apiKeyForm.name}-${suffix}`,
            permission: perm,
            expires_in_days: parseInt(this.apiKeyForm.expiresInDays, 10),
          };

          const response = await fetch("/api/auth/apikeys/create/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${this.auth.accessToken}`,
            },
            body: JSON.stringify(payload),
          });

          if (!response.ok) {
            let errorMessage = `Gagal membuat ${perm} key`;
            try {
              const errorData = await response.json();
              errorMessage = errorData.detail || errorMessage;
            } catch (parseError) {
              // Server returned HTML or non-JSON response
              errorMessage = `Server error (${response.status}): ${response.statusText}`;
            }
            throw new Error(errorMessage);
          }

          const apiKey = await response.json();
          createdKeys.push(apiKey);
        }

        // Reset form
        this.apiKeyForm.name = "";

        // Reload API keys to get the updated list
        await this.loadApiKeys();

        // Set new keys as defaults
        const writeKey = createdKeys.find((k) => k.permission === "write_only");
        const readKey = createdKeys.find((k) => k.permission === "read_only");

        if (writeKey) {
          this.setAsDefault(writeKey);
        }
        if (readKey) {
          this.setAsDefault(readKey);
        }

        console.log("✅ Kedua API key (write & read) berhasil dibuat!");
      } catch (error) {
        console.error("Create API keys error:", error);
        console.error(`❌ Gagal membuat API keys: ${error.message}`);
      } finally {
        this.apiKeyForm.isCreating = false;
      }
    },

    async loadApiKeys() {
      if (!this.auth.accessToken) return;

      this.apiKeys.isLoading = true;

      try {
        const response = await fetch("/api/auth/apikeys/", {
          headers: {
            Authorization: `Bearer ${this.auth.accessToken}`,
          },
        });

        if (!response.ok) {
          let errorMessage = "Gagal memuat API keys";
          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorMessage;
          } catch (parseError) {
            // Server returned HTML or non-JSON response
            errorMessage = `Server error (${response.status}): ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        this.apiKeys = data.map((key) => ({
          ...key,
          copied: false,
        }));

        // Validate and restore default keys after loading
        this.validateDefaultKeys();
      } catch (error) {
        console.error("Load API keys error:", error);
        console.error(`❌ Gagal memuat API keys: ${error.message}`);
      } finally {
        this.apiKeys.isLoading = false;
      }
    },

    async revokeApiKey(apiKey) {
      if (!confirm(`Yakin ingin menghapus API key "${apiKey.name}"?`)) return;

      try {
        const response = await fetch(`/api/auth/apikeys/revoke/${apiKey.id}/`, {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${this.auth.accessToken}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          let errorMessage = "Gagal menghapus API key";
          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorMessage;
          } catch (parseError) {
            // Server returned HTML or non-JSON response
            errorMessage = `Server error (${response.status}): ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        // Remove from local list
        this.apiKeys = this.apiKeys.filter((key) => key.id !== apiKey.id);

        // Clear default keys if the revoked key was a default
        if (this.defaultReadKey && this.defaultReadKey.id === apiKey.id) {
          console.log(`🗑️ Cleared default READ_ONLY key: ${apiKey.name}`);
          this.defaultReadKey = null;
          localStorage.removeItem("defaultReadKey");
        }
        if (this.defaultWriteKey && this.defaultWriteKey.id === apiKey.id) {
          console.log(`🗑️ Cleared default WRITE_ONLY key: ${apiKey.name}`);
          this.defaultWriteKey = null;
          localStorage.removeItem("defaultWriteKey");
        }

        // Clear from URL generator if it was being used
        if (this.urlGenerator.apiKey === apiKey.key) {
          this.urlGenerator.apiKey = "";
        }

        console.log("✅ API key berhasil dihapus!");
      } catch (error) {
        console.error("Revoke API key error:", error);
        console.error(`❌ Gagal menghapus API key: ${error.message}`);
      }
    },

    async copyApiKey(apiKey) {
      try {
        await navigator.clipboard.writeText(apiKey.key);
        apiKey.copied = true;
        setTimeout(() => {
          apiKey.copied = false;
        }, 2000);
      } catch (error) {
        // Fallback for older browsers
        const input = this.$refs[`apiKeyInput${apiKey.id}`][0];
        if (input) {
          input.select();
          // @ts-ignore - Used for older browser compatibility
          document.execCommand("copy");
          apiKey.copied = true;
          setTimeout(() => {
            apiKey.copied = false;
          }, 2000);
        }
      }
    },

    // Helper method to build URL with parameters
    buildUrl(baseUrl, urlType) {
      const params = new URLSearchParams();

      // Auto-attach API key based on URL type
      const apiKeyObj =
        urlType === "attendance" ? this.defaultWriteKey : this.defaultReadKey;

      if (apiKeyObj && apiKeyObj.key) {
        params.set("key", apiKeyObj.key);
        const permissionType =
          urlType === "attendance" ? "WRITE_ONLY" : "READ_ONLY";

        // Only log once per session to prevent console spam
        const logKey = urlType === "attendance" ? "writeKey" : "readKey";
        if (!this.loggedApiKeyUsage[logKey]) {
          console.log(
            `🔑 Using default ${permissionType} key: ${apiKeyObj.name}`,
          );
          this.loggedApiKeyUsage[logKey] = true;
        }
      } else {
        const permissionType =
          urlType === "attendance" ? "write_only" : "read_only";
        const urlContext =
          urlType === "attendance"
            ? "attendance (ngaji/asrama absen)"
            : urlType === "monitoring"
              ? "monitoring (pantau)"
              : "statistics (statistik)";

        // Only warn once per session to prevent console spam
        const warnKey =
          urlType === "attendance" ? "writeKeyWarning" : "readKeyWarning";
        if (!this.loggedApiKeyUsage[warnKey]) {
          console.warn(
            `⚠️ No default ${permissionType} API key set for ${urlContext}. Please set a default key in the API Key section.`,
          );
          this.loggedApiKeyUsage[warnKey] = true;
        }

        const permissionText = permissionType.replace("_", " ").toUpperCase();

        // Only show DOM warning once per session to prevent performance issues
        const domWarnKey =
          urlType === "attendance" ? "writeKeyWarning" : "readKeyWarning";
        if (
          !this.domWarningShown[domWarnKey] &&
          !document.querySelector(".api-key-warning")
        ) {
          this.domWarningShown[domWarnKey] = true;
          const warning = document.createElement("div");
          warning.className = "api-key-warning";
          warning.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #ff9500;
                        color: white;
                        padding: 12px 16px;
                        border-radius: 8px;
                        font-weight: 600;
                        z-index: 1000;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        max-width: 300px;
                        line-height: 1.4;
                    `;
          warning.innerHTML = `⚠️ Set default ${permissionText} key first!<br><small>Needed for ${urlContext}</small>`;
          document.body.appendChild(warning);

          setTimeout(() => {
            warning.remove();
          }, 5000);
        }
      }

      const extraParams = this.urlGenerator.extraParams;

      // Add parameters based on URL type
      if (urlType === "attendance") {
        this.addParam(params, "acara", extraParams.acara);
        this.addParam(params, "lokasi", extraParams.lokasi);
        this.addParam(params, "data", extraParams.data);
        this.addParam(params, "ph", extraParams.ph);
        if (this.urlGenerator.type === "asrama") {
          this.addParam(params, "sesi", extraParams.sesi);
        }
      } else if (urlType === "monitoring") {
        this.addParam(params, "acara", extraParams.acara);
        this.addParam(params, "lokasi", extraParams.lokasi);
        this.addParam(params, "tanggal", extraParams.tanggal);
      } else if (urlType === "statistics") {
        this.addParam(params, "acara", extraParams.acara);
        this.addParam(params, "lokasi", extraParams.lokasi);
        this.addParam(params, "tanggal", extraParams.tanggal);
        // Only add time parameter for ngaji/acara statistics
        if (this.urlGenerator.type === "acara") {
          this.addParam(params, "time", extraParams.time);
        }
        // For asrama statistics, add sesi parameter
        if (this.urlGenerator.type === "asrama") {
          this.addParam(params, "sesi", extraParams.sesi);
        }
      }

      return `${baseUrl}?${params.toString()}`;
    },

    addParam(params, key, value) {
      if (value && value.trim()) {
        params.set(key, value.trim());
      }
    },

    // URL Generator Methods
    generateUrls() {
      // Force regeneration of URLs and send to QR generator and URL shortener
      console.log("🚀 Generate URLs button clicked!");

      // Clear previous state
      this.copyStatus = [false, false, false];
      this.qrCodeUrl = null;
      this.shortUrls = [];

      // Always reveal the generated URLs section first
      this.showGeneratedUrls = true;

      // Get the absen URL from generated URLs
      const absenUrl = this.generatedUrls.find(
        (url) =>
          url.label.includes("Absensi") &&
          (url.url.includes("/ngaji?") || url.url.includes("/asrama?")),
      );

      console.log(
        `📱 Generated ${this.generatedUrls.length} URLs:`,
        this.generatedUrls.map((u) => u.label).join(", "),
      );

      if (absenUrl) {
        // Send URL to QR Code Generator
        this.qrGenerator.url = absenUrl.url;

        // Auto-generate QR code
        this.generateQR();

        // Automatically shorten all 3 URLs (absen, pantau, statistik)
        this.shortenAllGeneratedUrls();

        // Show success message
        console.log(
          "✅ URLs generated and sent to QR Code Generator & URL Shortener",
        );
      } else {
        console.warn("⚠️ No absen URL found, but URLs are still generated");
      }
    },

    async copyUrl(url, index) {
      try {
        await navigator.clipboard.writeText(url);
        this.$set(this.copyStatus, index, true);
        setTimeout(() => this.$set(this.copyStatus, index, false), 2000);
      } catch (error) {
        // Fallback for older browsers
        const input = this.$refs[`generatedUrlInput${index}`];
        if (input && input[0]) {
          input[0].select();
          // @ts-ignore - Used for older browser compatibility
          document.execCommand("copy");
          this.$set(this.copyStatus, index, true);
          setTimeout(() => this.$set(this.copyStatus, index, false), 2000);
        }
      }
    },

    async copyAllUrls() {
      const allUrls = this.generatedUrls
        .map((item) => `${item.label}:\n${item.url}`)
        .join("\n\n");

      try {
        await navigator.clipboard.writeText(allUrls);
        // Show success feedback for all
        this.copyStatus = [true, true, true];
        setTimeout(() => {
          this.copyStatus = [false, false, false];
        }, 2000);
      } catch (error) {
        console.error("Failed to copy all URLs:", error);
        console.error("❌ Gagal menyalin URL. Silakan copy satu per satu.");
      }
    },

    // QR Code Generator Methods
    generateQR() {
      console.log("🔍 generateQR called with URL:", this.qrGenerator.url);

      if (!this.qrGenerator.url) {
        console.log("❌ No URL in qrGenerator");
        return;
      }

      // Validate URL directly instead of using computed property
      const url = this.qrGenerator.url;
      const isAbsenUrl = url.includes("/ngaji") || url.includes("/asrama");
      const isPantauOrStatUrl =
        url.includes("/pantau") || url.includes("/stat");

      console.log(
        "🔍 URL validation - isAbsenUrl:",
        isAbsenUrl,
        "isPantauOrStatUrl:",
        isPantauOrStatUrl,
      );

      if (!isAbsenUrl || isPantauOrStatUrl) {
        console.log("❌ URL validation failed - not an absen URL");
        console.warn(
          "⚠️ QR Code hanya dapat dibuat untuk URL absensi (ngaji atau asrama)",
        );
        return;
      }

      console.log("✅ URL validation passed - generating QR code");

      // Using QR Server API for QR code generation
      const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/`;
      const params = new URLSearchParams({
        size: `${this.qrGenerator.size}x${this.qrGenerator.size}`,
        data: this.qrGenerator.url,
        format: "png",
        margin: "10",
        color: "1d1d1f",
        bgcolor: "ffffff",
      });

      this.qrCodeUrl = `${qrApiUrl}?${params.toString()}`;
    },

    downloadQR() {
      if (!this.qrCodeUrl) return;

      const link = document.createElement("a");
      link.href = this.qrCodeUrl;
      link.download = `qr-code-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    async generatePDF() {
      if (!this.qrCodeUrl || !this.qrGenerator.customText.trim()) return;

      try {
        // Import jsPDF dynamically, correctly handling the default export
        const { default: jsPDF } = await import("jspdf");

        // Create new PDF document with A5 size (148mm x 210mm)
        const doc = new jsPDF({
          orientation: "portrait",
          unit: "mm",
          format: "a5",
        });

        // Set margins (1.5cm = 15mm)
        const margin = 15;
        const pageWidth = 148;
        const pageHeight = 210;
        const contentWidth = pageWidth - 2 * margin;

        // Add "ABSENSI" title in bold (size 25)
        doc.setFont("helvetica", "bold");
        doc.setFontSize(25);
        const absensiText = "ABSENSI";
        const absensiWidth = doc.getTextWidth(absensiText);
        const absensiX = (pageWidth - absensiWidth) / 2;
        doc.text(absensiText, absensiX, margin + 20);

        // Add custom text below ABSENSI (size 18)
        doc.setFont("helvetica", "normal");
        doc.setFontSize(18);
        const customTextLines = doc.splitTextToSize(
          this.qrGenerator.customText.trim(),
          contentWidth,
        );
        let currentY = margin + 35;

        customTextLines.forEach((line) => {
          const lineWidth = doc.getTextWidth(line);
          const lineX = (pageWidth - lineWidth) / 2;
          doc.text(line, lineX, currentY);
          currentY += 8;
        });

        // Add minimal space before QR code
        currentY += 8;

        // Convert QR code image to base64 and add to PDF with aesthetic improvements
        const img = new Image();
        img.crossOrigin = "anonymous";

        await new Promise((resolve, reject) => {
          img.onload = () => {
            try {
              // Calculate QR code size to fit the margin (larger size)
              const qrPadding = 8; // Padding inside the border
              const borderWidth = 1; // Border thickness
              const maxQRSize = Math.min(contentWidth - 20, 100); // Larger max size, leave some margin
              const qrSize = maxQRSize;
              const totalBoxSize = qrSize + 2 * qrPadding + 2 * borderWidth;
              const qrX = (pageWidth - totalBoxSize) / 2;

              // Check if QR code fits on current page
              if (currentY + totalBoxSize > pageHeight - margin) {
                doc.addPage();
                currentY = margin + 20;
              }

              // Create canvas for QR code with rounded corners and border
              const canvas = document.createElement("canvas");
              const ctx = canvas.getContext("2d");
              const canvasSize = 400; // High resolution for better quality
              canvas.width = canvasSize;
              canvas.height = canvasSize;

              // Calculate dimensions for canvas
              const canvasPadding = (canvasSize * qrPadding) / totalBoxSize;
              const canvasBorder = (canvasSize * borderWidth) / totalBoxSize;
              const canvasQRSize =
                canvasSize - 2 * canvasPadding - 2 * canvasBorder;
              const cornerRadius = canvasSize * 0.05; // 5% of canvas size for rounded corners

              // Fill background with white
              ctx.fillStyle = "#ffffff";
              ctx.fillRect(0, 0, canvasSize, canvasSize);

              // Draw rounded rectangle background with border
              ctx.fillStyle = "#f8f9fa"; // Light gray background
              ctx.strokeStyle = "#dee2e6"; // Border color
              ctx.lineWidth = canvasBorder;

              // Function to draw rounded rectangle
              const drawRoundedRect = (x, y, width, height, radius) => {
                ctx.beginPath();
                ctx.moveTo(x + radius, y);
                ctx.lineTo(x + width - radius, y);
                ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
                ctx.lineTo(x + width, y + height - radius);
                ctx.quadraticCurveTo(
                  x + width,
                  y + height,
                  x + width - radius,
                  y + height,
                );
                ctx.lineTo(x + radius, y + height);
                ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
                ctx.lineTo(x, y + radius);
                ctx.quadraticCurveTo(x, y, x + radius, y);
                ctx.closePath();
              };

              // Draw background with rounded corners
              drawRoundedRect(0, 0, canvasSize, canvasSize, cornerRadius);
              ctx.fill();
              ctx.stroke();

              // Create a clipping path for the QR code area
              ctx.save();
              const qrStartX = canvasPadding + canvasBorder;
              const qrStartY = canvasPadding + canvasBorder;
              drawRoundedRect(
                qrStartX,
                qrStartY,
                canvasQRSize,
                canvasQRSize,
                cornerRadius * 0.7,
              );
              ctx.clip();

              // Draw the QR code image
              ctx.drawImage(
                img,
                qrStartX,
                qrStartY,
                canvasQRSize,
                canvasQRSize,
              );
              ctx.restore();

              // Convert canvas to base64
              const imgData = canvas.toDataURL("image/png");

              // Add the styled QR code to PDF
              doc.addImage(
                imgData,
                "PNG",
                qrX,
                currentY,
                totalBoxSize,
                totalBoxSize,
              );

              resolve();
            } catch (error) {
              reject(error);
            }
          };

          img.onerror = () => reject(new Error("Failed to load QR code image"));
          img.src = this.qrCodeUrl;
        });

        // Save the PDF
        const fileName = `absensi-qr-${Date.now()}.pdf`;
        doc.save(fileName);
      } catch (error) {
        console.error("Error generating PDF:", error);
        console.error(`❌ Gagal membuat PDF: ${error.message}`);
      }
    },

    // URL Shortener Methods
    async shortenUrl() {
      // Get all valid URLs with their labels
      const urlsToShorten = [];
      const urls = this.generatedUrls;

      if (urls && urls.length > 0) {
        // Always include absen URL
        const absenUrl = urls.find((u) => u.label.includes("Absensi"));
        if (absenUrl) urlsToShorten.push(absenUrl);

        // Include pantau and statistik URLs
        const pantauUrl = urls.find((u) => u.label.includes("Pantauan"));
        const statistikUrl = urls.find((u) => u.label.includes("Statistik"));
        if (pantauUrl) urlsToShorten.push(pantauUrl);
        if (statistikUrl) urlsToShorten.push(statistikUrl);
      }

      if (urlsToShorten.length === 0) {
        console.warn("⚠️ Silakan generate URL terlebih dahulu");
        return;
      }

      this.isShortening = true;
      this.shortenedUrls = [];
      this.shortUrlCopied = new Array(urlsToShorten.length).fill(false);

      try {
        const apiKeyObj = this.defaultWriteKey || this.defaultReadKey;
        if (!apiKeyObj || !apiKeyObj.key) {
          console.warn(
            "⚠️ API key diperlukan untuk memendekkan URL. Silakan set default API key terlebih dahulu.",
          );
          return;
        }

        for (const urlData of urlsToShorten) {
          const formData = new FormData();
          formData.append("url", urlData.url);

          const response = await fetch("/api/url/", {
            method: "POST",
            headers: {
              Authorization: `ApiKey ${apiKeyObj.key}`,
            },
            body: formData,
          });

          if (!response.ok) {
            let errorMessage = "Gagal memendekkan URL";
            try {
              const errorData = await response.json();
              errorMessage = errorData.detail || errorMessage;
            } catch (parseError) {
              errorMessage = `Server error (${response.status}): ${response.statusText}`;
            }
            throw new Error(errorMessage);
          }

          const data = await response.json();
          this.shortenedUrls.push({
            label: urlData.label,
            url: `${this.baseUrl}/s/${data.url_code}`,
            type: this.getUrlTypeFromLabel(urlData.label),
          });
        }
      } catch (error) {
        console.error("Error shortening URL:", error);
        console.error(`❌ Gagal memendekkan URL: ${error.message}`);
      } finally {
        this.isShortening = false;
      }
    },

    async copyShortUrl(index) {
      try {
        await navigator.clipboard.writeText(this.shortenedUrls[index].url);
        this.shortUrlCopied[index] = true;
        setTimeout(() => {
          this.shortUrlCopied[index] = false;
        }, 2000);
      } catch (error) {
        // Fallback for older browsers
        const input = this.$refs[`shortenedUrlInput${index}`][0];
        if (input) {
          input.select();
          // @ts-ignore - Used for older browser compatibility
          document.execCommand("copy");
          this.shortUrlCopied[index] = true;
          setTimeout(() => {
            this.shortUrlCopied[index] = false;
          }, 2000);
        }
      }
    },

    async copyAllShortUrls() {
      try {
        // Get the event name from URL parameters
        const eventName = this.urlGenerator.extraParams.acara || "Event";

        // Find specific URLs
        const absenUrl = this.shortenedUrls.find((url) =>
          url.label.includes("Absensi"),
        );
        const pantauUrl = this.shortenedUrls.find((url) =>
          url.label.includes("Pantau"),
        );
        const statistikUrl = this.shortenedUrls.find((url) =>
          url.label.includes("Statistik"),
        );

        // Create custom format
        const customText = `----------
LINK ABSENSI
${eventName}

ABSENSI
${absenUrl ? absenUrl.url : "URL tidak tersedia"}

PANTAU
${pantauUrl ? pantauUrl.url : "URL tidak tersedia"}

STATISTIK
${statistikUrl ? statistikUrl.url : "URL tidak tersedia"}
----------`;

        await navigator.clipboard.writeText(customText);
        console.log("✅ Format link absensi berhasil disalin!");
      } catch (error) {
        console.error("❌ Gagal menyalin URL. Silakan copy satu per satu.");
      }
    },

    // New Quick Action Methods
    generateQRForAbsen() {
      // Check if we have generated URLs
      if (!this.generatedUrls || this.generatedUrls.length === 0) {
        console.warn(
          "⚠️ Silakan klik tombol 'Generate URLs' terlebih dahulu di bagian Generator Parameter URL.",
        );
        return;
      }

      // Find the absen URL from generated URLs
      const absenUrl = this.generatedUrls.find(
        (url) =>
          url.label.includes("Absensi") &&
          (url.url.includes("/ngaji?") || url.url.includes("/asrama?")),
      );

      if (!absenUrl) {
        console.warn(
          "⚠️ URL absensi tidak ditemukan. Pastikan Anda telah generate URLs dengan benar.",
        );
        return;
      }

      // Set the URL to QR generator and generate QR code
      this.qrGenerator.url = absenUrl.url;
      this.generateQR();
    },

    shortenAllGeneratedUrls() {
      // This will use the existing shortenUrl method which already processes all URLs
      this.shortenUrl();
    },

    // Data Management Methods
    async fetchEndpointData() {
      if (!this.dataManager.selectedEndpoint) return;

      this.dataManager.isLoading = true;
      this.dataManager.error = null;
      this.dataManager.data = [];

      try {
        const apiKey = this.urlGenerator.apiKey || this.getUrlParameter("key");
        if (!apiKey) {
          throw new Error(
            "API key diperlukan untuk mengakses data. Silakan masukkan API key terlebih dahulu.",
          );
        }

        let url = `/api/${this.dataManager.selectedEndpoint}/`;
        if (this.dataManager.parameter) {
          url += `${encodeURIComponent(this.dataManager.parameter)}/`;
        }

        const response = await fetch(url, {
          headers: {
            Authorization: `ApiKey ${apiKey}`,
            Accept: "application/json",
          },
        });

        if (!response.ok) {
          let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorMessage;
          } catch (parseError) {
            // Server returned HTML or non-JSON response
            errorMessage = `Server error (${response.status}): ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        this.dataManager.data = Array.isArray(data) ? data : [data];
      } catch (error) {
        console.error("Fetch endpoint data error:", error);
        this.dataManager.error = error.message;
      } finally {
        this.dataManager.isLoading = false;
      }
    },

    exportData() {
      if (!this.dataManager.data.length) return;

      const dataStr = JSON.stringify(this.dataManager.data, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });

      const link = document.createElement("a");
      link.href = URL.createObjectURL(dataBlob);
      link.download = `${this.dataManager.selectedEndpoint.replace("/", "-")}-${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // API Testing Methods
    async testApiEndpoint() {
      if (!this.apiTester.selectedEndpoint) return;

      this.apiTester.isLoading = true;
      this.apiTester.error = null;
      this.apiTester.response = null;

      try {
        const apiKey = this.urlGenerator.apiKey || this.getUrlParameter("key");
        if (!apiKey) {
          throw new Error(
            "API key diperlukan untuk test API. Silakan masukkan API key terlebih dahulu.",
          );
        }

        let url = `/api/${this.apiTester.selectedEndpoint}/`;
        if (this.apiTester.method === "GET" && this.apiTester.queryParams) {
          url += `?${this.apiTester.queryParams}`;
        }

        const startTime = Date.now();
        const options = {
          method: this.apiTester.method,
          headers: {
            Authorization: `ApiKey ${apiKey}`,
            Accept: "application/json",
          },
        };

        if (this.apiTester.method === "POST") {
          if (this.apiTester.selectedEndpoint === "url") {
            // URL shortener expects FormData
            const formData = new FormData();
            try {
              const testData = JSON.parse(this.apiTester.testDataJson || "{}");
              Object.keys(testData).forEach((key) => {
                formData.append(key, testData[key]);
              });
              options.body = formData;
            } catch (e) {
              throw new Error("Invalid JSON format in test data");
            }
          } else {
            // Other endpoints expect JSON
            options.headers["Content-Type"] = "application/json";
            options.body = this.apiTester.testDataJson || "{}";
          }
        }

        const response = await fetch(url, options);
        const endTime = Date.now();

        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }

        this.apiTester.response = {
          status: response.status,
          ok: response.ok,
          time: endTime - startTime,
          data: responseData,
        };
      } catch (error) {
        console.error("API test error:", error);
        this.apiTester.error = error.message;
      } finally {
        this.apiTester.isLoading = false;
      }
    },

    // Additional Tools Methods
    generateQRFromManualInput() {
      if (!this.additionalTools.manualUrl.trim()) return;

      // Using QR Server API for QR code generation
      const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/`;
      const params = new URLSearchParams({
        size: `${this.additionalTools.qrSize}x${this.additionalTools.qrSize}`,
        data: this.additionalTools.manualUrl,
        format: "png",
        margin: "10",
        color: "1d1d1f",
        bgcolor: "ffffff",
      });

      this.additionalTools.qrCodeUrl = `${qrApiUrl}?${params.toString()}`;
    },

    downloadAdditionalQR() {
      if (!this.additionalTools.qrCodeUrl) return;

      const link = document.createElement("a");
      link.href = this.additionalTools.qrCodeUrl;
      link.download = `additional-qr-code-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    async generateAdditionalPDF() {
      if (
        !this.additionalTools.qrCodeUrl ||
        !this.additionalTools.customText.trim()
      )
        return;

      try {
        // Import jsPDF dynamically
        const { default: jsPDF } = await import("jspdf");

        // Create new PDF document with A5 size
        const doc = new jsPDF({
          orientation: "portrait",
          unit: "mm",
          format: "a5",
        });

        // Set margins
        const margin = 15;
        const pageWidth = 148;
        const pageHeight = 210;
        const contentWidth = pageWidth - 2 * margin;

        // Add "ABSENSI" title in bold
        doc.setFont("helvetica", "bold");
        doc.setFontSize(48);
        const absensiText = "ABSENSI";
        const absensiWidth = doc.getTextWidth(absensiText);
        const absensiX = (pageWidth - absensiWidth) / 2;
        doc.text(absensiText, absensiX, margin + 25);

        // Add custom text
        doc.setFont("helvetica", "normal");
        doc.setFontSize(14);
        const customTextLines = doc.splitTextToSize(
          this.additionalTools.customText.trim(),
          contentWidth,
        );
        let currentY = margin + 35;

        customTextLines.forEach((line) => {
          const lineWidth = doc.getTextWidth(line);
          const lineX = (pageWidth - lineWidth) / 2;
          doc.text(line, lineX, currentY);
          currentY += 7;
        });

        currentY += 8;

        // Add QR code
        const img = new Image();
        img.crossOrigin = "anonymous";

        await new Promise((resolve, reject) => {
          img.onload = () => {
            try {
              const qrPadding = 8;
              const borderWidth = 1;
              const maxQRSize = Math.min(contentWidth - 20, 100);
              const qrSize = maxQRSize;
              const totalBoxSize = qrSize + 2 * qrPadding + 2 * borderWidth;
              const qrX = (pageWidth - totalBoxSize) / 2;

              if (currentY + totalBoxSize > pageHeight - margin) {
                doc.addPage();
                currentY = margin + 20;
              }

              // Create canvas for styled QR code
              const canvas = document.createElement("canvas");
              const ctx = canvas.getContext("2d");
              const canvasSize = 400;
              canvas.width = canvasSize;
              canvas.height = canvasSize;

              const canvasPadding = (canvasSize * qrPadding) / totalBoxSize;
              const canvasBorder = (canvasSize * borderWidth) / totalBoxSize;
              const canvasQRSize =
                canvasSize - 2 * canvasPadding - 2 * canvasBorder;
              const cornerRadius = canvasSize * 0.05;

              // Fill background
              ctx.fillStyle = "#ffffff";
              ctx.fillRect(0, 0, canvasSize, canvasSize);

              // Draw rounded rectangle background
              ctx.fillStyle = "#f8f9fa";
              ctx.strokeStyle = "#dee2e6";
              ctx.lineWidth = canvasBorder;

              const drawRoundedRect = (x, y, width, height, radius) => {
                ctx.beginPath();
                ctx.moveTo(x + radius, y);
                ctx.lineTo(x + width - radius, y);
                ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
                ctx.lineTo(x + width, y + height - radius);
                ctx.quadraticCurveTo(
                  x + width,
                  y + height,
                  x + width - radius,
                  y + height,
                );
                ctx.lineTo(x + radius, y + height);
                ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
                ctx.lineTo(x, y + radius);
                ctx.quadraticCurveTo(x, y, x + radius, y);
                ctx.closePath();
              };

              drawRoundedRect(0, 0, canvasSize, canvasSize, cornerRadius);
              ctx.fill();
              ctx.stroke();

              // Clip and draw QR code
              ctx.save();
              const qrStartX = canvasPadding + canvasBorder;
              const qrStartY = canvasPadding + canvasBorder;
              drawRoundedRect(
                qrStartX,
                qrStartY,
                canvasQRSize,
                canvasQRSize,
                cornerRadius * 0.7,
              );
              ctx.clip();

              ctx.drawImage(
                img,
                qrStartX,
                qrStartY,
                canvasQRSize,
                canvasQRSize,
              );
              ctx.restore();

              const imgData = canvas.toDataURL("image/png");
              doc.addImage(
                imgData,
                "PNG",
                qrX,
                currentY,
                totalBoxSize,
                totalBoxSize,
              );

              resolve();
            } catch (error) {
              reject(error);
            }
          };

          img.onerror = () => reject(new Error("Failed to load QR code image"));
          img.src = this.additionalTools.qrCodeUrl;
        });

        // Save the PDF
        const fileName = `additional-absensi-qr-${Date.now()}.pdf`;
        doc.save(fileName);
      } catch (error) {
        console.error("Error generating PDF:", error);
        console.error(`❌ Gagal membuat PDF: ${error.message}`);
      }
    },

    async shortenManualUrl() {
      if (!this.additionalTools.manualUrl.trim()) return;

      this.additionalTools.isProcessing = true;
      this.additionalTools.shortenedManualUrl = "";

      try {
        const apiKeyObj = this.defaultWriteKey || this.defaultReadKey;
        if (!apiKeyObj || !apiKeyObj.key) {
          console.warn(
            "⚠️ API key diperlukan untuk memendekkan URL. Silakan set default API key terlebih dahulu.",
          );
          return;
        }

        const formData = new FormData();
        formData.append("url", this.additionalTools.manualUrl);

        const response = await fetch("/api/url/", {
          method: "POST",
          headers: {
            Authorization: `ApiKey ${apiKeyObj.key}`,
          },
          body: formData,
        });

        if (!response.ok) {
          let errorMessage = "Gagal memendekkan URL";
          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorMessage;
          } catch (parseError) {
            errorMessage = `Server error (${response.status}): ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();
        this.additionalTools.shortenedManualUrl = `${this.baseUrl}/s/${data.url_code}`;
      } catch (error) {
        console.error("Error shortening manual URL:", error);
        console.error(`❌ Gagal memendekkan URL: ${error.message}`);
      } finally {
        this.additionalTools.isProcessing = false;
      }
    },

    async copyAdditionalShortUrl() {
      try {
        await navigator.clipboard.writeText(
          this.additionalTools.shortenedManualUrl,
        );
        this.additionalTools.copied = true;
        setTimeout(() => {
          this.additionalTools.copied = false;
        }, 2000);
      } catch (error) {
        // Fallback for older browsers
        const input = this.$refs.additionalShortenedUrlInput;
        if (input) {
          input.select();
          // @ts-ignore - Used for older browser compatibility
          document.execCommand("copy");
          this.additionalTools.copied = true;
          setTimeout(() => {
            this.additionalTools.copied = false;
          }, 2000);
        }
      }
    },

    // Utility Methods
    getUrlParameter(name) {
      return new URLSearchParams(window.location.search).get(name) || "";
    },

    formatDate(dateString) {
      if (!dateString) return "Tidak diketahui";

      try {
        const date = new Date(dateString);
        return date.toLocaleDateString("id-ID", {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        });
      } catch (error) {
        return "Format tanggal tidak valid";
      }
    },
  },

  mounted() {
    // Set page title
    document.title = "Sistem Absensi Sabilillah";

    // Auto-populate API key from URL if available
    const apiKeyFromUrl = this.getUrlParameter("key");
    if (apiKeyFromUrl) {
      this.urlGenerator.apiKey = apiKeyFromUrl;
    }

    // Restore authentication state
    try {
      const saved = JSON.parse(localStorage.getItem(this.STORAGE_KEY) || "{}");
      if (saved.accessToken) {
        this.auth.username = saved.username || "";
        this.auth.accessToken = saved.accessToken;
        this.auth.isLoggedIn = true;

        // Verify token still valid by loading API keys
        this.loadApiKeys().catch(() => this.logout());
      }
    } catch (error) {
      console.error("Error restoring auth state:", error);
      this.logout();
    }

    // Restore default API keys
    try {
      const savedReadKey = localStorage.getItem("defaultReadKey");
      const savedWriteKey = localStorage.getItem("defaultWriteKey");

      if (savedReadKey) {
        this.defaultReadKey = JSON.parse(savedReadKey);
      }
      if (savedWriteKey) {
        this.defaultWriteKey = JSON.parse(savedWriteKey);
      }
    } catch (error) {
      console.error("Error restoring default API keys:", error);
      this.defaultReadKey = null;
      this.defaultWriteKey = null;
    }
  },
};
</script>

<style scoped>
/* Page structure */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
    Arial, sans-serif;
}

.content-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  min-height: 100vh;
}

.util-container {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Header styles */
.util-header {
  text-align: center;
  margin-bottom: 20px;
}

.util-title {
  font-size: 32px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  letter-spacing: -0.8px;
}

.util-subtitle {
  font-size: 18px;
  color: #8e8e93;
  margin: 0;
  font-weight: 500;
}

/* Tool section styles */
.tool-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 30px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.tool-section:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

.tool-header {
  margin-bottom: 24px;
  text-align: center;
}

.tool-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.tool-description {
  font-size: 16px;
  color: #8e8e93;
  margin: 0;
  font-weight: 500;
}

/* Form styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e5ea;
  border-radius: 16px;
  font-size: 17px;
  background: #f2f2f7;
  color: #1d1d1f;
  font-weight: 500;
  transition: all 0.2s ease;
  box-sizing: border-box;
  appearance: none;
}

.form-input::placeholder {
  color: #8e8e93;
  font-weight: 500;
}

.form-input:focus,
.form-select:focus {
  border-color: #007aff;
  background: white;
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.form-select {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 20px;
  padding-right: 50px;
}

.form-hint {
  display: block;
  font-size: 14px;
  color: #8e8e93;
  margin-top: 4px;
  font-weight: 400;
}

/* Parameter inputs */
.param-inputs {
  display: flex;
  gap: 12px;
}

.param-input {
  flex: 1;
}

/* URL display */
.url-display {
  display: flex;
  gap: 8px;
  align-items: center;
}

.url-input {
  flex: 1;
  font-family:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
    monospace;
  font-size: 14px;
  background: white;
  border-color: #007aff;
}

.copy-button {
  padding: 16px;
  border: none;
  border-radius: 16px;
  background: #007aff;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-button:hover {
  background: #0056cc;
  transform: translateY(-1px);
}

.copy-button:active {
  transform: translateY(0);
}

/* Action buttons */
.action-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 16px;
  background: #007aff;
  color: white;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: -0.3px;
}

.action-button:hover:not(:disabled) {
  background: #0056cc;
  transform: translateY(-1px);
}

.action-button:active {
  transform: translateY(0);
}

.action-button:disabled {
  background: #c7c7cc;
  cursor: not-allowed;
  transform: none;
}

.action-button.secondary {
  background: #34c759;
}

.action-button.secondary:hover:not(:disabled) {
  background: #28a745;
}

/* QR Code display */
.qr-result {
  margin-top: 20px;
  text-align: center;
}

.qr-display {
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 20px;
  border: 2px solid #e5e5ea;
  display: inline-block;
}

.qr-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
}

.qr-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.quick-action-button {
  padding: 14px 16px;
  border: 2px solid #e5e5ea;
  border-radius: 16px;
  background: #f2f2f7;
  color: #1d1d1f;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-action-button:hover:not(:disabled) {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  transform: translateY(-1px);
}

.quick-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.quick-action-button.danger {
  border-color: #ff3b30;
  color: #ff3b30;
}

.quick-action-button.danger:hover:not(:disabled) {
  background: rgba(255, 59, 48, 0.1);
  border-color: #ff3b30;
}

/* Authentication styles */
.auth-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(52, 199, 89, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.welcome-text {
  margin: 0;
  font-size: 16px;
  color: #1d1d1f;
  font-weight: 500;
}

.section-subtitle {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 16px 0;
  letter-spacing: -0.3px;
}

.api-key-creation {
  padding: 20px;
  background: rgba(0, 122, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(0, 122, 255, 0.1);
}

.api-keys-list {
  padding: 20px;
  background: rgba(142, 142, 147, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(142, 142, 147, 0.1);
}

.api-key-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e5ea;
  margin-bottom: 12px;
  gap: 16px;
}

.api-key-item:last-child {
  margin-bottom: 0;
}

.api-key-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.api-key-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-key-default {
  background: #34c759;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.api-key-details {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.api-key-permission {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.api-key-permission.read_only {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.api-key-permission.write_only {
  background: rgba(255, 149, 0, 0.1);
  color: #ff9500;
  border: 1px solid rgba(255, 149, 0, 0.2);
}

.api-key-expires {
  font-size: 12px;
  color: #8e8e93;
}

.api-key-value {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 8px;
}

.api-key-input {
  font-family:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
    monospace;
  font-size: 12px;
  background: #f2f2f7;
  border-color: #e5e5ea;
  flex: 1;
}

.api-key-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.auto-action-button {
  padding: 8px 12px;
  border: 2px solid #e5e5ea;
  border-radius: 12px;
  background: #f2f2f7;
  color: #1d1d1f;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 80px;
}

.auto-action-button:hover {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  transform: translateY(-1px);
}

.auto-action-button:active {
  transform: translateY(0);
}

/* Expired API Keys */
.expired-keys-list {
  padding: 20px;
  background: rgba(255, 59, 48, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 59, 48, 0.1);
  margin-top: 20px;
}

.expired-notice {
  padding: 12px 16px;
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.2);
  border-radius: 12px;
  margin-bottom: 16px;
}

.expired-notice p {
  margin: 0;
  color: #ff3b30;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.api-key-item.expired {
  opacity: 0.7;
  background: #fafafa;
}

.api-key-permission.expired {
  background: rgba(142, 142, 147, 0.1);
  color: #8e8e93;
  border: 1px solid rgba(142, 142, 147, 0.2);
}

.api-key-expires.expired {
  color: #ff3b30;
  font-weight: 600;
}

.copy-button.small {
  padding: 12px;
  min-width: 48px;
  font-size: 14px;
}

.action-button.small {
  padding: 8px 16px;
  font-size: 14px;
  width: auto;
  margin-top: 0;
}

.action-button.danger {
  background: #ff3b30;
}

.action-button.danger:hover:not(:disabled) {
  background: #d70015;
}

.error-message {
  padding: 12px 16px;
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.2);
  border-radius: 12px;
  color: #ff3b30;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

/* Parameter sections */
.param-section {
  margin-top: 20px;
  padding: 20px;
  background: rgba(52, 199, 89, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(52, 199, 89, 0.1);
}

/* Data Management styles */
.data-result {
  margin-top: 20px;
  padding: 20px;
  background: rgba(142, 142, 147, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(142, 142, 147, 0.1);
}

.data-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e5ea;
}

.data-table-container {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid #e5e5ea;
  background: white;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e5ea;
}

.data-table th {
  background: #f2f2f7;
  font-weight: 600;
  color: #1d1d1f;
  position: sticky;
  top: 0;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-note {
  padding: 12px 16px;
  margin: 0;
  font-size: 12px;
  color: #8e8e93;
  background: #f2f2f7;
  border-top: 1px solid #e5e5ea;
}

/* API Testing styles */
.form-textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e5ea;
  border-radius: 16px;
  font-size: 14px;
  background: #f2f2f7;
  color: #1d1d1f;
  font-weight: 500;
  transition: all 0.2s ease;
  box-sizing: border-box;
  font-family:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
    monospace;
  resize: vertical;
}

.form-textarea:focus {
  border-color: #007aff;
  background: white;
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.api-response {
  margin-top: 20px;
  padding: 20px;
  background: rgba(255, 149, 0, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 149, 0, 0.1);
}

.response-info {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e5ea;
}

.response-status {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.response-status.success {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.response-status.error {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: 1px solid rgba(255, 59, 48, 0.2);
}

.response-time {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.response-body {
  background: #1d1d1f;
  color: #ffffff;
  padding: 16px;
  border-radius: 12px;
  font-size: 12px;
  font-family:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
    monospace;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Generated/shortened URL results */
.generated-url,
.generated-urls,
.shortened-result {
  margin-top: 20px;
  padding: 20px;
  background: rgba(0, 122, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

.url-item {
  margin-bottom: 16px;
}

.url-item:last-child {
  margin-bottom: 0;
}

.url-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.key-type-badge {
  color: white;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.url-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 122, 255, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .util-container {
    max-width: 100%;
  }

  .tool-section {
    padding: 24px;
    margin: 0 10px;
  }

  .util-title {
    font-size: 28px;
  }

  .tool-title {
    font-size: 20px;
  }

  .param-inputs {
    flex-direction: column;
    gap: 8px;
  }

  .url-display {
    flex-direction: column;
    gap: 12px;
  }

  .url-item {
    margin-bottom: 12px;
  }

  .url-label {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .url-actions {
    margin-top: 12px;
    padding-top: 12px;
  }

  .copy-button {
    width: 100%;
  }

  .user-info {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .api-key-item {
    flex-direction: column;
    gap: 12px;
  }

  .api-key-details {
    justify-content: center;
  }

  .api-key-value {
    flex-direction: column;
    gap: 8px;
  }

  .copy-button.small {
    width: 100%;
  }

  .api-key-actions {
    flex-direction: column;
    gap: 8px;
  }

  .auto-action-button {
    width: 100%;
    min-width: auto;
  }

  .qr-actions {
    flex-direction: column;
    gap: 12px;
  }

  .qr-actions .action-button {
    width: 100%;
  }

  /* Data management responsive */
  .data-summary {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .data-table-container {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px;
  }

  /* API testing responsive */
  .response-info {
    flex-direction: column;
    gap: 8px;
  }

  .form-textarea {
    font-size: 14px;
  }

  .response-body {
    font-size: 11px;
  }
}

/* iPhone 11/XR and similar devices (414px viewport) */
@media (max-width: 430px) {
  .content-area {
    padding: 12px;
  }

  .util-container {
    max-width: 100%;
    margin: 0;
  }

  .tool-section {
    padding: 20px;
    margin: 0 4px 16px 4px;
    border-radius: 16px;
  }

  .util-title {
    font-size: 26px;
    margin-bottom: 8px;
  }

  .util-subtitle {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .tool-title {
    font-size: 18px;
    margin-bottom: 16px;
  }

  /* Form inputs optimized for iPhone */
  .param-inputs {
    flex-direction: column;
    gap: 12px;
  }

  .param-input {
    padding: 12px 16px;
    font-size: 16px; /* Prevent zoom on iOS */
    border-radius: 12px;
    min-height: 44px;
  }

  .param-label {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .quick-action-button {
    padding: 14px 20px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 12px;
  }

  /* URL display */
  .url-display {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
  }

  .url-item {
    margin-bottom: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid #e5e5ea;
  }

  .url-label {
    font-size: 14px;
    font-weight: 600;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    color: #1d1d1f;
    margin-bottom: 8px;
    display: block;
  }

  .url-actions {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 122, 255, 0.2);
  }

  .url-text {
    font-size: 14px;
    word-break: break-all;
    line-height: 1.4;
  }

  .copy-button {
    width: 100%;
    padding: 12px 16px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 12px;
  }

  /* User info section */
  .user-info {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 16px;
  }

  .user-name {
    font-size: 18px;
  }

  .user-role {
    font-size: 14px;
  }

  /* API key management */
  .api-key-item {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
  }

  .api-key-details {
    justify-content: center;
    text-align: center;
  }

  .api-key-name {
    font-size: 16px;
    flex-wrap: wrap;
    gap: 6px;
  }

  .api-key-default {
    font-size: 9px;
    padding: 1px 5px;
    border-radius: 10px;
  }

  .api-key-default {
    font-size: 10px;
    padding: 1px 6px;
  }

  .api-key-value {
    flex-direction: column;
    gap: 8px;
  }

  .api-key-text {
    font-size: 12px;
    word-break: break-all;
    padding: 8px;
    border-radius: 8px;
  }

  .copy-button.small {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    min-height: 36px;
  }

  .api-key-actions {
    flex-direction: column;
    gap: 8px;
  }

  .auto-action-button {
    width: 100%;
    min-width: auto;
    padding: 10px 16px;
    font-size: 14px;
    min-height: 40px;
    border-radius: 10px;
  }

  /* QR code section */
  .qr-container {
    padding: 16px;
    text-align: center;
  }

  .qr-code {
    max-width: 200px;
    height: auto;
    border-radius: 12px;
  }

  .qr-actions {
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
  }

  .qr-actions .action-button {
    width: 100%;
    padding: 12px 16px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 12px;
  }

  /* Form sections */
  .form-group {
    margin-bottom: 16px;
  }

  .form-label {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .form-input,
  .form-select {
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 12px;
    min-height: 44px;
    width: 100%;
  }

  /* Action buttons */
  .action-button {
    padding: 14px 20px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 12px;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .content-area {
    padding: 10px;
  }

  .tool-section {
    padding: 20px;
    margin: 0 5px;
  }

  .util-title {
    font-size: 24px;
  }

  .util-subtitle {
    font-size: 16px;
  }
}

/* Quick Action Section Styles */
.quick-action-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  text-align: center;
}

.quick-action-section .quick-action-button {
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  border: none;
  padding: 14px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  width: 100%;
  max-width: 300px;
}

.quick-action-section .quick-action-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.quick-action-section .quick-action-button:disabled {
  background: rgba(255, 255, 255, 0.5);
  color: rgba(102, 126, 234, 0.5);
  cursor: not-allowed;
}

.quick-action-section .form-hint {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

/* Additional Tools Section Styles */
.additional-tool-section {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.additional-tool-section .section-subtitle {
  color: #495057;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.additional-tool-section .form-group {
  margin-bottom: 16px;
}

.additional-tool-section .action-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  margin-bottom: 16px;
}

.additional-tool-section .action-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  transform: translateY(-1px);
}

.additional-tool-section .action-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
</style>
